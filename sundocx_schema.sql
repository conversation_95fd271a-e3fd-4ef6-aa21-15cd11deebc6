-- Create the database
CREATE DATABASE sundocx_db;

-- Create the user with password (replace 'password' with a secure password)
CREATE USER sundocx_user WITH PASSWORD 'password';

-- Grant all privileges on the database to the user
GRANT ALL PRIVILEGES ON DATABASE sundocx_db TO sundocx_user;

-- Connect to the new database
\c sundocx_db

-- Now create all tables and objects from your schema:

-- Create enum types
CREATE TYPE public.document_status AS ENUM (
    'PENDING',
    'SUCCESS',
    'FAILED'
);

CREATE TYPE public.job_status AS ENUM (
    'completed',
    'failed'
);

-- Create tables
CREATE TABLE public.admin (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    email character varying(100) NOT NULL,
    password text NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE SEQUENCE public.admin_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.admin_id_seq OWNED BY public.admin.id;

CREATE TABLE public.clients (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    description text,
    created_by integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    email character varying(255),
    phone character varying(20),
    address text,
    status integer
);

CREATE SEQUENCE public.clients_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.clients_id_seq OWNED BY public.clients.id;

CREATE TABLE public.documents (
    id integer NOT NULL,
    file_name character varying NOT NULL,
    file_path text NOT NULL,
    file_type character varying,
    description text,
    process_type character varying,
    user_id integer,
    client_id integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE SEQUENCE public.documents_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.documents_id_seq OWNED BY public.documents.id;

CREATE TABLE public.extracted_data (
    id integer NOT NULL,
    job_id integer,
    data jsonb,
    extracted_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.extracted_data_files (
    id integer NOT NULL,
    extracted_data_id integer,
    job_id integer NOT NULL,
    file_name text NOT NULL,
    data jsonb,
    created_at timestamp with time zone DEFAULT now()
);

CREATE SEQUENCE public.extracted_data_files_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.extracted_data_files_id_seq OWNED BY public.extracted_data_files.id;

CREATE SEQUENCE public.extracted_data_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.extracted_data_id_seq OWNED BY public.extracted_data.id;

CREATE TABLE public.jobs (
    id integer NOT NULL,
    job_name character varying(255),
    template_id integer,
    user_id integer,
    client_id integer,
    status character varying(50) DEFAULT 'In Progress'::character varying,
    description text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    job_type character varying(10),
    document_id integer[] DEFAULT '{}'::integer[],
    CONSTRAINT jobs_job_type_check CHECK (((job_type)::text = ANY ((ARRAY['SEW'::character varying, 'YVW'::character varying])::text[])))
);

CREATE SEQUENCE public.jobs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.jobs_id_seq OWNED BY public.jobs.id;

CREATE TABLE public.logs (
    id integer NOT NULL,
    job_id integer,
    message text,
    log_level character varying(20),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE SEQUENCE public.logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.logs_id_seq OWNED BY public.logs.id;

CREATE TABLE public.templates (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    config_json text,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_by integer NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);

CREATE SEQUENCE public.templates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.templates_id_seq OWNED BY public.templates.id;

CREATE TABLE public.users (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    email character varying(100) NOT NULL,
    password text NOT NULL,
    is_active boolean DEFAULT true,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    api_token character varying(255),
    phone character varying(20)
);

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;

-- Set default values
ALTER TABLE ONLY public.admin ALTER COLUMN id SET DEFAULT nextval('public.admin_id_seq'::regclass);
ALTER TABLE ONLY public.clients ALTER COLUMN id SET DEFAULT nextval('public.clients_id_seq'::regclass);
ALTER TABLE ONLY public.documents ALTER COLUMN id SET DEFAULT nextval('public.documents_id_seq'::regclass);
ALTER TABLE ONLY public.extracted_data ALTER COLUMN id SET DEFAULT nextval('public.extracted_data_id_seq'::regclass);
ALTER TABLE ONLY public.extracted_data_files ALTER COLUMN id SET DEFAULT nextval('public.extracted_data_files_id_seq'::regclass);
ALTER TABLE ONLY public.jobs ALTER COLUMN id SET DEFAULT nextval('public.jobs_id_seq'::regclass);
ALTER TABLE ONLY public.logs ALTER COLUMN id SET DEFAULT nextval('public.logs_id_seq'::regclass);
ALTER TABLE ONLY public.templates ALTER COLUMN id SET DEFAULT nextval('public.templates_id_seq'::regclass);
ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);

-- Add constraints
ALTER TABLE ONLY public.admin
    ADD CONSTRAINT admin_email_key UNIQUE (email);

ALTER TABLE ONLY public.admin
    ADD CONSTRAINT admin_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.extracted_data_files
    ADD CONSTRAINT extracted_data_files_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.extracted_data
    ADD CONSTRAINT extracted_data_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.jobs
    ADD CONSTRAINT jobs_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.logs
    ADD CONSTRAINT logs_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.templates
    ADD CONSTRAINT templates_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);

-- Add foreign keys
ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id) ON DELETE SET NULL;

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;

ALTER TABLE ONLY public.extracted_data_files
    ADD CONSTRAINT extracted_data_files_extracted_data_id_fkey FOREIGN KEY (extracted_data_id) REFERENCES public.extracted_data(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.extracted_data
    ADD CONSTRAINT extracted_data_job_id_fkey FOREIGN KEY (job_id) REFERENCES public.jobs(id) ON DELETE CASCADE;

ALTER TABLE ONLY public.logs
    ADD CONSTRAINT logs_job_id_fkey FOREIGN KEY (job_id) REFERENCES public.jobs(id) ON DELETE CASCADE;

-- Grant privileges to the user on all tables
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO sundocx_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO sundocx_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO sundocx_user;
