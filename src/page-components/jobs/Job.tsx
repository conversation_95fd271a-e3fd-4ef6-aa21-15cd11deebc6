"use client";
import { useState, useEffect, useMemo } from "react";
import UIDataTable from "@/ui-components/table";
import Grid from "@mui/material/Grid";
import { GridColDef } from "@mui/x-data-grid";
import ActionMenu from "@/ui-components/table/ActionMenu";
import { UIDataTableProps } from "@/ui-components/table/types";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import { useJobs } from "@/hooks/useJobs";
import AppDialog from "@/ui-components/dialog/Dialog";
import AddEditJobs from "./helper-component/Add-EditJob";
import { DialogMaxWidthProps } from "@/ui-components/dialog/types";
import ViewModel from "./helper-component/ViewModel";
import {
  Badge,
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Paper,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { DeleteJob } from "./helper-component/DeleteJob";
import IconButton from "@/theme/components/buttons/IconButton";
import { Icon } from "@iconify/react/dist/iconify.js";
import React from "react";
import ClientSelection from "@/core-components/dropDown/job/clientSelection";
import JobTypeSelection from "@/core-components/dropDown/job/jobTypeSelection";
import UserSelection from "@/core-components/dropDown/job/userSelection";
import {
  getButtonStyles,
  getFileIcon,
  getStatusConfig,
} from "@/config/jobCongif/jobConfig";
import DocumentBoxView from "./helper-component/documentBoxView/DocumentBoxView";

const JobComponent = () => {
  const {
    state: {
      searchText,
      page,
      pageSize,
      isFormModalOpen,
      selectedJobs,
      selectedClient,
      selectedJobType,
      selectedUser,
    },
    jobDatas,
    jobDataLoading,
    clientData,
    userData,
    onFormModalHandle,
    handleFormSubmit,
    handleRunClick,
    handlePageChange,
    handlePageSizeChange,
    handleSearchInputChange,
    handleClientChange,
    handleJobTypeChange,
    handleUserChange,
  } = useJobs();

  const actions = [
    {
      id: 1,
      icon: "ic:baseline-edit",
      title: "Edit",
      onItemClick: (jobs: any) => {
        onFormModalHandle("edit", jobs);
      },
    },
    {
      id: 3,
      icon: "ic:baseline-delete-outline",
      title: "Delete",
      onItemClick: (jobs: any) => {
        onFormModalHandle("delete", jobs);
      },
    },
  ];

  const columns: GridColDef[] = [
    {
      field: "__check__",
      headerName: "",
      width: 52,
      sortable: false,
      disableColumnMenu: true,
    },
    {
      field: "id",
      headerName: "JOB ID",
      align: "left",
      flex: 1,
      minWidth: 90,
      disableColumnMenu: true,
    },
    {
      field: "user_name",
      headerName: "USER NAME",
      align: "left",
      flex: 1,
      minWidth: 130,
      disableColumnMenu: true,
      renderCell: (params) => {
        const userName = params?.row?.user?.name;
        return (
          <Typography variant="body2" fontWeight={600} sx={{ marginTop: 2 }}>
            {userName || "-"}
          </Typography>
        );
      },
    },
    {
      field: "job_name",
      headerName: "JOB NAME",
      align: "left",
      flex: 1,
      minWidth: 100,
      disableColumnMenu: true,
    },
    {
      field: "job_type",
      headerName: "JOB TYPE",
      align: "left",
      flex: 1,
      minWidth: 100,
      disableColumnMenu: true,
    },
    {
      field: "user",
      headerName: "CLIENT",
      headerAlign: "left",
      align: "left",
      editable: false,
      flex: 2,
      minWidth: 70,
      disableColumnMenu: true,
      renderCell: (params) => {
        const clientName = params?.row?.client?.name;

        return (
          <Typography variant="body2" fontWeight={600} sx={{ marginTop: 2 }}>
            {clientName || "-"}
          </Typography>
        );
      },
    },
    // {
    //   field: "template_id",
    //   headerName: "TEMPLATE NAME",
    //   align: "left",
    //   flex: 2,
    //   minWidth: 120,
    //   disableColumnMenu: true,
    //   renderCell: (params) => {
    //     const templateName = params?.row?.template?.name;
    //     return (
    //       <Typography variant="body2" fontWeight={600} sx={{ marginTop: 2 }}>
    //         {templateName || "-"}
    //       </Typography>
    //     );
    //   },
    // },

    {
      field: "document_id",
      headerName: "DOCUMENT",
      align: "center",
      headerAlign: "center",
      flex: 2,
      minWidth: 200,
      disableColumnMenu: true,
      renderCell: (params) => {
        const documents = params?.row?.documents;

        if (!documents || documents.length === 0) {
          return (
            <Box display="flex" justifyContent="center" width="100%">
              <Typography variant="body2" fontWeight={600}>
                -
              </Typography>
            </Box>
          );
        }

        return <DocumentBoxView documents={documents} />;
      },
    },
    {
      field: "description",
      headerName: "DESCRIPTION",
      align: "left",
      flex: 2,
      minWidth: 160,
      disableColumnMenu: true,
      renderCell: (params) => {
        const description = params?.value;
        const truncatedDescription =
          description && description.length > 20
            ? `${description.slice(0, 20)}...`
            : description || "-";

        return (
          <Typography variant="body2" fontWeight={600} sx={{ marginTop: 2 }}>
            {truncatedDescription}
          </Typography>
        );
      },
    },

    {
      field: "date",
      headerName: "DATE",
      align: "left",
      flex: 1,
      minWidth: 110,
      disableColumnMenu: true,
      renderCell: () => {
        return <>{new Date().toLocaleDateString()}</>;
      },
    },
    {
      field: "status",
      headerName: "STATUS",
      align: "left",
      flex: 2,
      minWidth: 140,
      disableColumnMenu: true,
      renderCell: (params) => {
        const status = params?.row?.status;

        const { bg, text, icon } = getStatusConfig(status);

        return (
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              backgroundColor: bg,
              color: text,
              px: 2,
              py: 0.5,
              borderRadius: "15px",
              border: `1px solid ${text}20`,
              width: "25px",
              minWidth: 120,
              boxShadow: "0 1px 2px rgba(0,0,0,0.05)",
              marginTop: 1.2,
            }}
          >
            {icon}
            <Typography variant="caption" sx={{ fontWeight: 600 }}>
              {status}
            </Typography>
          </Box>
        );
      },
    },

    // RUN COLUMN
    {
      field: "run",
      headerName: "RUN",
      flex: 2,
      minWidth: 100,
      disableColumnMenu: true,
      renderCell: (params) => {
        const status = params.row?.status;
        const isCompleted = status === "Completed";
        const isFailed = status === "Failed";
        const isTodo = status === "To do";

        const getButtonLabel = () => (isFailed ? "Re-Run" : "Run");

        return (
          <Tooltip title={isCompleted ? "Already completed" : ""}>
            <span>
              <Button
                variant="outlined"
                size="small"
                onClick={() => handleRunClick(params.row)}
                disabled={isCompleted}
                sx={getButtonStyles({ isCompleted, isFailed, isTodo })}
              >
                {getButtonLabel()}
              </Button>
            </span>
          </Tooltip>
        );
      },
    },
    {
      field: "action",
      headerAlign: "right",
      headerName: "ACTION",
      align: "right",
      flex: 1,
      minWidth: 50,
      disableColumnMenu: true,
      renderCell: (params) => (
        <ActionMenu actions={actions} params={params.row} />
      ),
    },
  ];

  const ToolbarItems = useMemo(
    () => (
      <>
        <ClientSelection
          clientValue={selectedClient}
          onclientChange={handleClientChange}
          clientData={clientData}
        />
        <JobTypeSelection
          jobTypeValue={selectedJobType}
          onJobTypeChange={handleJobTypeChange}
          jobData={jobDatas}
        />
        <UserSelection
          userValue={selectedUser}
          onUserChange={handleUserChange}
          userData={userData}
        />
        <IconifyIcon
          icon="mdi:folder-add"
          sx={{ fontSize: "20px" }}
          onClick={() => onFormModalHandle("add")}
        />
      </>
    ),
    [
      selectedClient,
      selectedUser,
      selectedJobType,
      clientData,
      jobDatas,
      userData,
      handleJobTypeChange,
      handleUserChange,
      handleClientChange,
    ]
  );

  const TableDataProps = useMemo(
    () => ({
      tableData: jobDatas?.data,
      totalPage: jobDatas?.pagination?.totalPages,
      page,
      pageSize,
      handlePageChange,
      handlePageSizeChange,
    }),
    [jobDatas, page, pageSize, handlePageChange, handlePageSizeChange]
  );

  const TableProps: UIDataTableProps = useMemo(
    () => ({
      tableName: "Jobs",
      columns,
      searchText,
      tableDataProps: TableDataProps,
      ToolbarItems,
      refetch: "",
      handlePageChange,
      handlePageSizeChange,
      handleSearchInputChange,
    }),
    [
      columns,
      searchText,
      TableDataProps,
      ToolbarItems,
      handlePageChange,
      handlePageSizeChange,
      handleSearchInputChange,
    ]
  );

  const DialogMap: Record<string, React.ReactNode> = {
    add: (
      <AddEditJobs
        closeModal={() => onFormModalHandle()}
        handleFormSubmit={handleFormSubmit}
      />
    ),
    edit: (
      <AddEditJobs
        closeModal={() => onFormModalHandle()}
        handleFormSubmit={handleFormSubmit}
        selectedJobs={selectedJobs?.jobs}
      />
    ),
    view: <ViewModel />,
    delete: (
      <DeleteJob
        selectedJobs={selectedJobs}
        onClose={() => onFormModalHandle()}
      />
    ),
  };

  const DialogTitleMap: Record<string, string> = {
    add: "Add Jobs",
    edit: "Edit Jobs",
    delete: "Delete Jobs",
    view: `view ( ${selectedJobs?.jobs?.document?.file_name} )`,
  };

  const dialogType = selectedJobs?.type || "";
  const DialogComponent = DialogMap[dialogType] || null;
  const DialogTitleText = DialogTitleMap[dialogType] || "Client Data";

  const DialogProps = {
    title: DialogTitleText,
    open: isFormModalOpen,
    handleClose: () => onFormModalHandle(),
    body: DialogComponent,
    maxWidth: dialogType === "view" ? "xl" : ("sm" as DialogMaxWidthProps),
  };

  if (jobDataLoading) {
    return <LinearProgress />;
  }

  return (
    <>
      <Grid>
        <UIDataTable {...TableProps} />
      </Grid>
      <AppDialog {...DialogProps} />
    </>
  );
};

export default JobComponent;
