import { getFileIcon } from "@/config/jobCongif/jobConfig";
import { Icon } from "@iconify/react/dist/iconify.js";
import {
  Box,
  Badge,
  Dialog,
  DialogTitle,
  Typography,
  DialogContent,
  Paper,
  ListItemIcon,
  ListItemText,
  DialogActions,
  Button,
} from "@mui/material";

import React, { useState } from "react";

interface DocumentBoxViewProps {
  documents: any[];
}

const DocumentBoxView: React.FC<DocumentBoxViewProps> = ({ documents }) => {
  const [open, setOpen] = useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  return (
    <>
      <Box display="flex" justifyContent="center" width="100%">
        <Box
          display="flex"
          alignItems="center"
          sx={{ cursor: "pointer" }}
          onClick={handleOpen}
          marginTop={1}
        >
          <Badge
            badgeContent={documents.length}
            color="primary"
            overlap="circular"
            anchorOrigin={{
              vertical: "top",
              horizontal: "right",
            }}
          >
            <Icon
              icon="flat-color-icons:folder"
              width={35}
              height={40}
              style={{ marginRight: 10 }}
            />
          </Badge>
        </Box>

        <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
          <DialogTitle>
            <Typography variant="h6" fontWeight={700}>
              📁 Documents ({documents.length})
            </Typography>
          </DialogTitle>

          <DialogContent
            dividers
            sx={{
              backgroundColor: "#f9fafb",
              padding: 2,
            }}
          >
            {documents.map((doc: any, index: number) => {
              const { icon, color } = getFileIcon(doc.file_type);

              return (
                <Paper
                  key={index}
                  elevation={2}
                  sx={{
                    marginBottom: 1.1,
                    paddingY: 1,
                    paddingX: 1.5,
                    borderRadius: 2,
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                    "&:hover": {
                      backgroundColor: "#f0f0f0",
                    },
                  }}
                >
                  <ListItemIcon>
                    <Icon icon={icon} color={color} width={26} height={26} />
                  </ListItemIcon>
                  <ListItemText
                    sx={{ cursor: "pointer" }}
                    primary={
                      <Typography variant="body1" fontWeight={600}>
                        {doc.file_name || "-"}
                      </Typography>
                    }
                    secondary={
                      <Typography variant="caption" color="text.secondary">
                        {doc.file_type?.toUpperCase() || "Unknown Type"}
                      </Typography>
                    }
                  />
                </Paper>
              );
            })}
          </DialogContent>

          <DialogActions sx={{ backgroundColor: "#f9fafb" }}>
            <Button onClick={handleClose} variant="contained" color="primary">
              Close
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </>
  );
};

export default DocumentBoxView;
