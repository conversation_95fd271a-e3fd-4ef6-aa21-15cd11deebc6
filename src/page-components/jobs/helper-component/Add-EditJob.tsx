"use client";
import { useCallback, useEffect, useMemo } from "react";
import FormLayout from "@/ui-components/form/layout/FormLayout";
import { useForm } from "react-hook-form";
import AppButton from "@/ui-components/button/Button";
import TextInput from "@/ui-components/form/hooks/TextInput";
import { zodResolver } from "@hookform/resolvers/zod";
import { AddEditJobsProps } from "../types";
import { defaultValuesSchema } from "@/lib/job";
import SelectInput from "@/ui-components/form/hooks/SelectInput";
import {
  useFetchTemplateOptions,
  useFetchDocumentOptions,
  useFetchClientOptions,
} from "@/config/job";
import { z } from "zod";
import { jobTypeOption } from "@/config/documentCongif/documentCongif";
import { useUserContext } from "@/context/userContext/UserContext";
import MultiSelectInput from "@/ui-components/form/hooks/MultiSelectInput";

const AddEditJobs = ({
  closeModal,
  handleFormSubmit,
  selectedJobs,
}: AddEditJobsProps) => {
  const { user } = useUserContext();
  const userId = user?.id;

  const documentOptions = useFetchDocumentOptions();
  const templateOptions = useFetchTemplateOptions();
  const clientOptions = useFetchClientOptions();

  const defaultValues = useMemo(
    () => ({
      user_id: userId,
      client_id: 0,
      job_name: "",
      job_type: "SEW",
      // template_id: null,
      document_id: [],
      description: "",
      status: "To do",
    }),
    [userId]
  );

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<z.infer<typeof defaultValuesSchema>>({
    resolver: zodResolver(defaultValuesSchema),
    defaultValues: {},
  });

  useEffect(() => {
    if (selectedJobs) {
      reset({
        ...selectedJobs,
        user_id: selectedJobs.user_id || userId,
        client_id: selectedJobs.clientId || 0,
        // template_id: selectedJobs.templateId || "",
        document_id: Array.isArray(selectedJobs.documentId)
          ? selectedJobs.documentId.map((id) => Number(id))
          : [],
        job_name: selectedJobs.job_name || "",
        job_type: selectedJobs.job_type || "SEW",
        description: selectedJobs.description || "",
        status: selectedJobs.status || "To do",
      });
    } else {
      reset(defaultValues);
    }
  }, [selectedJobs, reset, defaultValues, userId]);

  const handleFormSubmitWrapper = useCallback(
    (data: any) => {
      handleFormSubmit({
        ...data,
        id: selectedJobs?.id,
        document_id: data.document_id.map((id: any) => Number(id)),
      });
      closeModal();
    },
    [handleFormSubmit, closeModal, selectedJobs]
  );

  return (
    <form onSubmit={handleSubmit(handleFormSubmitWrapper)}>
      <FormLayout>
        <TextInput
          name="job_name"
          control={control}
          label="Job Name *"
          helperText={errors.job_name?.message}
        />

        <SelectInput
          name="job_type"
          control={control}
          label="Job Type *"
          helperText={errors.job_type?.message}
          options={jobTypeOption}
        />
        {/* 
        <SelectInput
          name="template_id"
          control={control}
          label="Template Name *"
          helperText={errors.template_id?.message}
          options={templateOptions}
        /> */}
        <SelectInput
          name="client_id"
          control={control}
          label="Client Name *"
          helperText={errors.client_id?.message}
          options={clientOptions}
        />

        <MultiSelectInput
          name="document_id"
          control={control}
          label="Document Name *"
          helperText={errors.document_id?.message}
          options={documentOptions}
        />
        <TextInput
          name="description"
          control={control}
          label="Description"
          helperText={errors.description?.message}
        />
      </FormLayout>
      <AppButton type="submit">Save</AppButton>
      <AppButton type="button" onClick={closeModal}>
        Cancel
      </AppButton>
    </form>
  );
};

export default AddEditJobs;
