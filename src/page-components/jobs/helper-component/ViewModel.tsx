"use client";
import * as React from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TablePagination from "@mui/material/TablePagination";
import TableRow from "@mui/material/TableRow";
import Button from "@mui/material/Button";
import { saveAs } from "file-saver";
import { Column, columns } from "./jobTable/Column";
import { Box, TextField, Typography } from "@mui/material";
import SaveIcon from "@mui/icons-material/Save";
import CancelIcon from "@mui/icons-material/Cancel";
import { useUpdateExtractedData } from "@/hooks/useExtracteData";
import CircularProgress from "@mui/material/CircularProgress";

const columnGroups = columns.reduce((acc: Record<string, Column[]>, column) => {
  if (!acc[column.group]) acc[column.group] = [];
  acc[column.group].push(column);
  return acc;
}, {});

const ViewModel = ({ extractedIdData, extractedPopupLoading }: any) => {
  const [rows, setRows] = React.useState<any[]>([]);
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(10);
  const [editingRow, setEditingRow] = React.useState<number | null>(null);
  const [editedRowData, setEditedRowData] = React.useState<any>(null);
  const updateMutation = useUpdateExtractedData() as any;
  const handleRowClick = (rowIndex: number) => {
    if (editingRow !== null) return;
    setEditingRow(rowIndex);
    setEditedRowData({ ...rows[rowIndex] });
  };

  const handleCellChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    columnId: string
  ) => {
    setEditedRowData({
      ...editedRowData,
      [columnId]: e.target.value,
    });
  };

  const handleSave = async () => {
    if (editingRow !== null && editedRowData) {
      try {
        // Call the mutation to update the data

        // Update local state only after successful API call
        const updatedRows = [...rows];
        updatedRows[editingRow] = editedRowData;
        setRows(updatedRows);

        setEditingRow(null);
        setEditedRowData(null);

        if (extractedIdData?.id) {
          await updateMutation.mutateAsync({
            id: extractedIdData?.id,
            data: editedRowData,
          });
        }
      } catch (error) {
        console.error("Failed to save data:", error);
        // You might want to show an error message to the user here
      }
    }
  };

  const handleCancel = () => {
    setEditingRow(null);
    setEditedRowData(null);
  };

  const getCountry = (state: string): string => {
    const australianStates = [
      "NSW",
      "VIC",
      "QLD",
      "SA",
      "WA",
      "TAS",
      "NT",
      "ACT",
    ];
    return australianStates.includes(state?.toUpperCase())
      ? "AUSTRALIA"
      : "INTL";
  };

  const formatDate = (dateStr: string) => {
    if (!dateStr) return "";
    const cleanStr = dateStr.replace(/\D/g, "");
    if (cleanStr.length >= 8) {
      const day = cleanStr.substring(0, 2);
      const month = cleanStr.substring(2, 4);
      const year = cleanStr.substring(4, 8);
      return `${year}-${month}-${day}`;
    }
    return dateStr;
  };

  const transformDataToRows = (data: any) => {
    const rows: any[] = [];
    const imageData = extractedIdData?.job?.document?.file_name;

    const propertyAddress = data.title_and_transaction?.property_address || {};

    const toUpperCase = (value: string | undefined) =>
      value ? value.toUpperCase() : undefined;

    const createRow = (vendorData: any, purchaserData: any) => {
      const row: any = {
        // General Information
        ...(imageData && { Image: imageData }),
        ...(data?.document_type && { DocumentType: data.document_type }),
        EmailHardCopy: "Email",
        ...(data?.document_type && {
          Template: data.document_type === "NOD" ? "NODT1" : "NOAT1",
        }),
        Handwritten: "No",

        // Property Information
        ...((propertyAddress.flat_unit_number || propertyAddress.flatunit) && {
          FlatUnitNumber:
            propertyAddress.flat_unit_number || propertyAddress.flatunit,
        }),
        ...(propertyAddress.street_number && {
          StreetNumber: propertyAddress.street_number,
        }),
        ...(propertyAddress.street_name && {
          StreetName: toUpperCase(propertyAddress.street_name),
        }),
        ...(propertyAddress.street_type && {
          StreetType: toUpperCase(propertyAddress.street_type),
        }),
        ...(propertyAddress.suburb && {
          Suburb: toUpperCase(propertyAddress.suburb),
        }),
        ...(propertyAddress.postcode && { Postcode: propertyAddress.postcode }),
        ...(data.title_and_transaction?.lot_number && {
          LotNumber: data.title_and_transaction.lot_number,
        }),
        ...(data.title_and_transaction?.plan_number && {
          PlanNumber: data.title_and_transaction.plan_number,
        }),
        ...(data.title_and_transaction?.date_of_transfer && {
          DateOfTransfer: formatDate(
            data.title_and_transaction.date_of_transfer
          ),
        }),
      };

      // Add vendor data if present
      if (vendorData) {
        const vendorAddress = vendorData.address || {};
        const vendorFutureAddress =
          vendorData.address_for_future_correspondence || {};

        Object.assign(row, {
          // Vendor information based on is_individual
          ...(vendorData.is_individual === "No" &&
            vendorData.individual_text && {
              CompanyName1: toUpperCase(vendorData.individual_text),
            }),
          ...(vendorData.is_individual !== "No" &&
            vendorData.surname && {
              Surname1: toUpperCase(vendorData.surname),
            }),
          ...(vendorData.is_individual !== "No" &&
            (vendorData.given_names || vendorData.other_names) && {
              GivenNames1: toUpperCase(
                vendorData.given_names || vendorData.other_names
              ),
            }),
          ...(vendorData.acn && { ACNNumber1: vendorData.acn }),
          ...(vendorData.email && { Email1: vendorData.email }),
          ...((vendorData.phone || vendorData.phone_number) && {
            PhoneNumber1: vendorData.phone || vendorData.phone_number,
          }),

          // Vendor Address
          ...((vendorAddress.flat_unit_number || vendorAddress.flatunit) && {
            FlatUnitNumber1:
              vendorAddress.flat_unit_number || vendorAddress.flatunit,
          }),
          ...(vendorAddress.street_number && {
            StreetNumber1: vendorAddress.street_number,
          }),
          ...(vendorAddress.street_name && {
            StreetName1: toUpperCase(vendorAddress.street_name),
          }),
          ...(vendorAddress.street_type && {
            StreetType1: toUpperCase(vendorAddress.street_type),
          }),
          ...(vendorAddress.suburb && {
            Suburb1: toUpperCase(vendorAddress.suburb),
          }),
          ...(vendorAddress.postcode && { Postcode1: vendorAddress.postcode }),
          ...(vendorAddress.state && {
            State1: vendorAddress.state,
            Country1: getCountry(vendorAddress.state),
          }),

          // Vendor Future Address
          ...((vendorFutureAddress.flat_unit_number ||
            vendorFutureAddress.flatunit) && {
            FlatUnitNumber2:
              vendorFutureAddress.flat_unit_number ||
              vendorFutureAddress.flatunit,
          }),
          ...(vendorFutureAddress.street_number && {
            StreetNumber2: vendorFutureAddress.street_number,
          }),
          ...(vendorFutureAddress.street_name && {
            StreetName2: toUpperCase(vendorFutureAddress.street_name),
          }),
          ...(vendorFutureAddress.street_type && {
            StreetType2: toUpperCase(vendorFutureAddress.street_type),
          }),
          ...(vendorFutureAddress.suburb && {
            Suburb2: toUpperCase(vendorFutureAddress.suburb),
          }),
          ...(vendorFutureAddress.postcode && {
            Postcode2: vendorFutureAddress.postcode,
          }),
          ...(vendorFutureAddress.state && {
            State2: vendorFutureAddress.state,
            Country2: getCountry(vendorFutureAddress.state),
          }),

          ...(vendorData.principal_place_of_residence && {
            PrincipalPlaceOfResidence: vendorData.principal_place_of_residence,
          }),
        });
      }

      // Add purchaser data if present
      if (purchaserData) {
        const purchaserAddress = purchaserData.address || {};
        const purchaserFutureAddress =
          purchaserData.address_for_future_correspondence || {};

        Object.assign(row, {
          // Purchaser information based on is_individual
          ...(purchaserData.is_individual === "No" &&
            purchaserData.individual_text && {
              CompanyName2: toUpperCase(purchaserData.individual_text),
            }),
          ...(purchaserData.is_individual !== "No" &&
            purchaserData.surname && {
              Surname2: toUpperCase(purchaserData.surname),
            }),
          ...(purchaserData.is_individual !== "No" &&
            (purchaserData.given_names || purchaserData.other_names) && {
              GivenNames2: toUpperCase(
                purchaserData.given_names || purchaserData.other_names
              ),
            }),
          ...(purchaserData.acn && { ACNNumber2: purchaserData.acn }),
          ...(purchaserData.email && { Email2: purchaserData.email }),
          ...((purchaserData.phone || purchaserData.phone_number) && {
            PhoneNumber2: purchaserData.phone || purchaserData.phone_number,
          }),
          ...(purchaserData.date_of_birth && {
            DateOfBirth2: formatDate(purchaserData.date_of_birth),
          }),

          // Purchaser Address
          ...((purchaserAddress.flat_unit_number ||
            purchaserAddress.flatunit) && {
            FlatUnitNumber3:
              purchaserAddress.flat_unit_number || purchaserAddress.flatunit,
          }),
          ...(purchaserAddress.street_number && {
            StreetNumber3: purchaserAddress.street_number,
          }),
          ...(purchaserAddress.street_name && {
            StreetName3: toUpperCase(purchaserAddress.street_name),
          }),
          ...(purchaserAddress.street_type && {
            StreetType3: toUpperCase(purchaserAddress.street_type),
          }),
          ...(purchaserAddress.suburb && {
            Suburb3: toUpperCase(purchaserAddress.suburb),
          }),
          ...(purchaserAddress.postcode && {
            Postcode3: purchaserAddress.postcode,
          }),
          ...(purchaserAddress.state && {
            State3: purchaserAddress.state,
            Country3: getCountry(purchaserAddress.state),
          }),

          // Purchaser Future Address
          ...((purchaserFutureAddress.flat_unit_number ||
            purchaserFutureAddress.flatunit) && {
            FlatUnitNumber4:
              purchaserFutureAddress.flat_unit_number ||
              purchaserFutureAddress.flatunit,
          }),
          ...(purchaserFutureAddress.street_number && {
            StreetNumber4: purchaserFutureAddress.street_number,
          }),
          ...(purchaserFutureAddress.street_name && {
            StreetName4: toUpperCase(purchaserFutureAddress.street_name),
          }),
          ...(purchaserFutureAddress.street_type && {
            StreetType4: toUpperCase(purchaserFutureAddress.street_type),
          }),
          ...(purchaserFutureAddress.suburb && {
            Suburb4: toUpperCase(purchaserFutureAddress.suburb),
          }),
          ...(purchaserFutureAddress.postcode && {
            Postcode4: purchaserFutureAddress.postcode,
          }),
          ...(purchaserFutureAddress.state && {
            State4: purchaserFutureAddress.state,
            Country4: getCountry(purchaserFutureAddress.state),
          }),

          ...(purchaserData.principal_place_of_residence && {
            PrincipalPlaceOfResidence:
              purchaserData.principal_place_of_residence,
          }),
        });
      }

      return row;
    };

    const transferors = data.transferors || [];
    const transferees = data.transferees || data.transferee || [];

    // If there's exactly one vendor and one purchaser, combine them in one row
    if (transferors.length === 1 && transferees.length === 1) {
      rows.push(createRow(transferors[0], transferees[0]));
    }
    // Otherwise create separate rows for each vendor and purchaser
    else {
      // Create rows for each vendor (with no purchaser)
      transferors.forEach((transferor: any) => {
        rows.push(createRow(transferor, null));
      });

      // Create rows for each purchaser (with no vendor)
      transferees.forEach((transferee: any) => {
        rows.push(createRow(null, transferee));
      });
    }

    // If no vendors or purchasers, create an empty row
    if (rows.length === 0) {
      rows.push(createRow(null, null));
    }

    return rows;
  };

  React.useEffect(() => {
    if (extractedIdData?.data) {
      const transformedRows = transformDataToRows(extractedIdData.data);
      setRows(transformedRows);
    }
  }, [extractedIdData]);

  const handleDownloadCSV = () => {
    const headers = columns.map((col) => `"${col.label}"`).join(",");
    const csvRows = rows.map((row) =>
      columns.map((col) => `"${row[col.id] ?? ""}"`).join(",")
    );
    const csvContent = [headers, ...csvRows].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    saveAs(blob, "FileDocsExport.csv");
  };

  const handleChangePage = (_: unknown, newPage: number) => setPage(newPage);
  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  if (extractedPopupLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
          width: "100%",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 2,
          }}
        >
          <CircularProgress />
          <Typography variant="h6">Loading data...</Typography>
        </Box>
      </Box>
    );
  }
  return (
    <>
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 1 }}>
          <Button
            variant="contained"
            onClick={handleDownloadCSV}
            sx={{ borderRadius: "1px" }}
          >
            Download CSV
          </Button>

          {editingRow !== null && (
            <>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSave}
                sx={{
                  borderRadius: "1px",
                  backgroundColor: "green",
                  "&:hover": {
                    backgroundColor: "green",
                  },
                }}
              >
                Save
              </Button>
              <Button
                variant="contained"
                startIcon={<CancelIcon />}
                onClick={handleCancel}
                sx={{
                  borderRadius: "1px",
                  color: "white",
                  backgroundColor: "red",
                  "&:hover": {
                    backgroundColor: "red",
                  },
                }}
              >
                Cancel
              </Button>
            </>
          )}
        </Box>
      </Box>

      <Paper sx={{ width: "100%", overflow: "auto" }}>
        <TableContainer sx={{ height: 600, overflowY: "auto" }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                {Object.entries(columnGroups).map(([groupName, groupCols]) => (
                  <TableCell
                    key={groupName}
                    align="center"
                    colSpan={groupCols.length}
                    sx={{
                      fontWeight: "bold",
                      backgroundColor: "#f5f5f5",
                      borderRight: "1px solid #e0e0e0",
                    }}
                  >
                    {groupName}
                  </TableCell>
                ))}
              </TableRow>
              <TableRow>
                {columns.map((column) => (
                  <TableCell
                    key={column.id}
                    align={column.align}
                    style={{
                      minWidth: column.minWidth,
                      backgroundColor: "#f9f9f9",
                      fontWeight: "bold",
                      borderRight: "1px solid #e0e0e0",
                    }}
                  >
                    {column.label}
                  </TableCell>
                ))}
                <TableCell
                  style={{
                    minWidth: 100,
                    backgroundColor: "#f9f9f9",
                    fontWeight: "bold",
                  }}
                >
                  {/* Empty cell for actions column header */}
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {rows
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row, rowIndex) => (
                  <TableRow
                    hover
                    key={rowIndex}
                    sx={{
                      cursor: "pointer",
                      backgroundColor:
                        editingRow === rowIndex ? "#f5f5f5" : "inherit",
                    }}
                  >
                    {columns.map((column) => (
                      <TableCell
                        key={column.id}
                        align={column.align}
                        sx={{
                          borderRight: "1px solid #f0f0f0",
                          padding: "8px",
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                        }}
                        onClick={() => handleRowClick(rowIndex)}
                      >
                        {editingRow === rowIndex ? (
                          <TextField
                            variant="standard"
                            value={editedRowData[column.id] || ""}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => handleCellChange(e, column.id)}
                            fullWidth
                            InputProps={{
                              disableUnderline: true,
                              style: { fontSize: "inherit" },
                            }}
                          />
                        ) : (
                          row[column.id] || ""
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[10, 25, 100]}
          component="div"
          count={rows.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </>
  );
};

export default ViewModel;
