export type JobsProps = {
  id?: number;
  user_id: string | number;
  clientId: string | number;
  job_name: string;
  job_type: string;
  templateId: string | number;
  documentId: [];
  description: string;
  status: string;
};

export type AddEditJobsProps = {
  selectedJobs?: JobsProps | null;
  closeModal: () => void;
  handleFormSubmit: (data: JobsProps) => void;
};


export type RowsProps = {
  id: number;
  name: string;
  description: string;
  updated_at: string;
};
