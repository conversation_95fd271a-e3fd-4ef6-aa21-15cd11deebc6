"use client";
import { UserLogin } from "@/services/login/type";
import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSignInForm } from "@/hooks/useLogin";
import {
  Box,
  Button,
  IconButton,
  InputAdornment,
  Link,
  Stack,
  Typography,
} from "@mui/material";
import TextInput from "@/ui-components/form/hooks/TextInput";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import { SignInFormSchema } from "@/lib/login";

const defaultValues = {
  email: "",
  password: "",
};
const SignInForm = () => {
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<UserLogin>({
    resolver: zodResolver(SignInFormSchema),
    defaultValues: defaultValues,
  });

  const { showPassword, isPending, onSubmit, togglePasswordVisibility } =
    useSignInForm();
  return (
    <Stack
      mx="auto"
      width={410}
      height="auto"
      minHeight={800}
      direction="column"
      alignItems="center"
      justifyContent="space-between"
    >
      <Box width={1} sx={{ mt: 15 }}>
        <Typography variant="h3">Sign In</Typography>
        <Typography mt={1.5} variant="body2" color="text.disabled">
          Enter your email and password to sign in!
        </Typography>

        <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 6 }}>
          <TextInput
            name="email"
            control={control}
            label="Email *"
            helperText={errors.email?.message}
          />

          <TextInput
            name="password"
            control={control}
            label="Password *"
            helperText={errors.password?.message}
            type={showPassword ? "text" : "password"}
            endAdornment={
              <InputAdornment position="end">
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={togglePasswordVisibility}
                  edge="end"
                >
                  <IconifyIcon
                    icon={
                      showPassword
                        ? "ic:outline-visibility"
                        : "ic:outline-visibility-off"
                    }
                    color="neutral.main"
                  />
                </IconButton>
              </InputAdornment>
            }
          />

          <Button
            type="submit"
            variant="contained"
            size="large"
            sx={{ mt: 3 }}
            fullWidth
            disabled={isPending}
          >
            {isPending ? "Signing In..." : "Sign In"}
          </Button>
        </Box>
      </Box>

      <Typography variant="body2" color="text.disabled" fontWeight={500}>
        © {new Date().getFullYear()} Sundocx, Made with by{""}❤️
        <Link href="#" target="_blank" rel="noreferrer" fontWeight={600}>
          Genpixels Tech Pvt Ltd
        </Link>
      </Typography>
    </Stack>
  );
};

export default SignInForm;
