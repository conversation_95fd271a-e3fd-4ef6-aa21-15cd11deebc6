// src/page-components/extracteData/ExtracteData.tsx
"use client";
import { useExtracteDataHooks } from "@/hooks/useExtracteData";
import UIDataTable from "@/ui-components/table";
import { Grid, LinearProgress, Tooltip, Typography } from "@mui/material";
import { useMemo } from "react";
import { GridColDef } from "@mui/x-data-grid";
import { DialogMaxWidthProps } from "@/ui-components/dialog/types";
import AppDialog from "@/ui-components/dialog/Dialog";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import ViewModel from "../jobs/helper-component/ViewModel";
import { useRouter } from "next/navigation";
import CustomDatePicker from "@/ui-components/datepicker/Datepicker";
import ClientSelection from "@/core-components/dropDown/job/clientSelection";
import dayjs from "dayjs";
import JobTypeSelection from "@/core-components/dropDown/job/jobTypeSelection";

const ExtracteData = () => {
  const router = useRouter();
  const {
    state: {
      searchText,
      page,
      pageSize,
      isFormModalOpen,
      selectedExtractedData,
      selectedClient,
      selectedJobType,
      startDate,
    },
    extractedData,
    extractedIdData,
    extracteDataLoading,
    extractedPopupLoading,
    handlePageChange,
    handlePageSizeChange,
    onFormModalHandle,
    handleSearchInputChange,
    handleClientChange,
    handleJobTypeChange,
    handleDateChange,
    clientData,
    jobDatas,
  } = useExtracteDataHooks();

  const handleView = (data: any) => {
    router.push(`/management/extracteData/${data.id}/${data?.type}`);
  };

  const columns: GridColDef[] = [
    { field: "__check__", headerName: "", width: 52 },
    { field: "id", headerName: "ID", flex: 1, minWidth: 10 },
    {
      field: "job_name",
      headerName: "JOB NAME",
      flex: 1,
      minWidth: 140,
      renderCell: (params) => (
        <Typography fontWeight={600} mt={2}>
          {params.row?.job?.job_name || "-"}
        </Typography>
      ),
    },
    {
      field: "document_id",
      headerName: "DOCUMENT NAME",
      align: "left",
      flex: 2,
      minWidth: 270,
      disableColumnMenu: true,
      renderCell: (params) => {
        const documents = params?.row?.job?.documents;
        if (!documents || documents.length === 0) {
          return (
            <Typography variant="body2" fontWeight={600} sx={{ marginTop: 2 }}>
              -
            </Typography>
          );
        }

        const tooltipContent = documents
          .map((doc: any) => doc.file_name || "-")
          .join("\n");

        const displayText =
          documents.length === 1
            ? documents[0].file_name
            : `${documents[0].file_name} +${documents.length - 1} more`;

        return (
          <Tooltip
            title={
              <pre style={{ whiteSpace: "pre-line", margin: 0, lineHeight: 3 }}>
                {tooltipContent}
              </pre>
            }
            arrow
          >
            <Typography
              variant="body2"
              fontWeight={600}
              marginTop={2}
              sx={{
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                maxWidth: 250,
                cursor: "pointer",
              }}
            >
              {displayText}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: "job_type",
      headerName: "JOB TYPE",
      flex: 1,
      minWidth: 130,
      renderCell: (params) => {
        const jobType = params?.row?.job?.job_type;

        return (
          <Typography fontWeight={600} mt={2}>
            {jobType || "-"}
          </Typography>
        );
      },
    },
    {
      field: "extracted_at",
      headerName: "DATE",
      flex: 1,
      minWidth: 160,
      renderCell: (params) => {
        const extracted_at = params?.row?.extracted_at;
        const formattedDate = extracted_at
          ? dayjs(extracted_at).format("YYYY/MM/DD")
          : "-";

        return (
          <Typography fontWeight={600} mt={2}>
            {formattedDate}
          </Typography>
        );
      },
    },
    {
      field: "status",
      headerName: "STATUS",
      flex: 1,
      minWidth: 80,
      renderCell: (params) => {
        const status = params.row?.job?.status;

        return (
          <Typography fontWeight={600} mt={2}>
            {status || "-"}
          </Typography>
        );
      },
    },
    {
      field: "view",
      headerName: "VIEW JSON",
      headerAlign: "center",
      align: "center",
      editable: false,
      flex: 1,
      minWidth: 120,
      disableColumnMenu: true,
      renderCell: (params) => {
        console.log("params", params);

        return (
          <IconifyIcon
            icon="ic:baseline-remove-red-eye"
            sx={{ fontSize: "20px", cursor: "pointer" }}
            onClick={() =>
              handleView({
                id: params.row.id,
                type: params.row?.job?.job_type,
                ...params.row,
              })
            }
          />
        );
      },
    },
  ];

  const DialogMap: Record<string, React.ReactNode> = {
    view: (
      <ViewModel
        extractedIdData={extractedIdData}
        extractedPopupLoading={extractedPopupLoading}
      />
    ),
  };

  const ToolbarItems = useMemo(() => {
    return (
      <>
        <ClientSelection
          clientValue={selectedClient}
          onclientChange={handleClientChange}
          clientData={clientData}
        />
        <JobTypeSelection
          jobTypeValue={selectedJobType}
          onJobTypeChange={handleJobTypeChange}
          jobData={jobDatas}
        />
        <CustomDatePicker
          label="Date"
          value={startDate}
          onChange={handleDateChange}
        />
      </>
    );
  }, [
    selectedClient,
    selectedJobType,
    handleClientChange,
    clientData,
    jobDatas,
    startDate,
    handleDateChange,
    handleJobTypeChange,
  ]);

  const TableDataProps = useMemo(
    () => ({
      tableData: extractedData?.data,
      totalPage: extractedData?.pagination?.totalPages,
      page,
      pageSize,
      handlePageChange,
      handlePageSizeChange,
    }),
    [extractedData, page, pageSize, handlePageChange, handlePageSizeChange]
  );

  const TableProps: any = useMemo(
    () => ({
      tableName: "Extracted Data",
      columns,
      searchText,
      tableDataProps: TableDataProps,
      ToolbarItems: ToolbarItems,
      refetch: "",
      handlePageChange,
      handlePageSizeChange,
      handleSearchInputChange,
    }),
    [
      columns,
      searchText,
      TableDataProps,
      handlePageChange,
      handlePageSizeChange,
      handleSearchInputChange,
    ]
  );

  const DialogTitleMap: Record<string, string> = {
    add: "Add Jobs",
    edit: "Edit Jobs",
    delete: "Delete Jobs",
    view: `view ( ${selectedExtractedData?.job?.job_name} )`,
  };

  const dialogType = selectedExtractedData?.type || "";
  const DialogComponent = DialogMap[dialogType] || null;
  const DialogTitleText = DialogTitleMap[dialogType] || "Client Data";

  const DialogProps = {
    title: DialogTitleText,
    open: isFormModalOpen,
    handleClose: () => onFormModalHandle(),
    body: DialogComponent,
    maxWidth: dialogType === "view" ? "xl" : ("sm" as DialogMaxWidthProps),
  };

  if (extracteDataLoading) {
    return <LinearProgress />;
  }

  return (
    <>
      <Grid>
        <UIDataTable {...TableProps} />
      </Grid>
      <AppDialog {...DialogProps} />
    </>
  );
};

export default ExtracteData;
