export interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: "right";
  format?: (value: any) => string;
  group: string;
}

export const sewColumns: Column[] = [
  // General Information
  { id: "Image", label: "Image", minWidth: 150, group: "GeneralInformation" },
  {
    id: "DocumentType",
    label: "DocumentType",
    minWidth: 150,
    group: "GeneralInformation",
  },
  {
    id: "EmailHardCopy",
    label: "EmailHardCopy",
    minWidth: 150,
    group: "GeneralInformation",
  },
  {
    id: "Template",
    label: "Template",
    minWidth: 150,
    group: "GeneralInformation",
  },
  {
    id: "Handwritten",
    label: "Handwritten",
    minWidth: 150,
    group: "GeneralInformation",
  },

  // Property Information
  {
    id: "FlatUnitNumber",
    label: "FlatUnitNumber",
    minWidth: 150,
    group: "PropertyInformation",
  },
  {
    id: "StreetNumber",
    label: "StreetNumber",
    minWidth: 150,
    group: "PropertyInformation",
  },
  {
    id: "StreetName",
    label: "StreetName",
    minWidth: 150,
    group: "PropertyInformation",
  },
  {
    id: "StreetType",
    label: "StreetType",
    minWidth: 150,
    group: "PropertyInformation",
  },
  {
    id: "Suburb",
    label: "Suburb",
    minWidth: 150,
    group: "PropertyInformation",
  },
  {
    id: "Postcode",
    label: "Postcode",
    minWidth: 150,
    group: "PropertyInformation",
  },
  {
    id: "LotNumber",
    label: "LotNumber",
    minWidth: 150,
    group: "PropertyInformation",
  },
  {
    id: "PlanNumber",
    label: "PlanNumber",
    minWidth: 150,
    group: "PropertyInformation",
  },
  {
    id: "DateOfTransfer",
    label: "DateOfTransfer",
    minWidth: 150,
    group: "PropertyInformation",
  },

  // Vendor
  { id: "CompanyName1", label: "CompanyName1", minWidth: 150, group: "Vendor" },
  { id: "ACNNumber1", label: "ACNNumber1", minWidth: 150, group: "Vendor" },
  { id: "Surname1", label: "Surname1", minWidth: 150, group: "Vendor" },
  { id: "GivenNames1", label: "GivenNames1", minWidth: 150, group: "Vendor" },
  { id: "Email1", label: "Email1", minWidth: 150, group: "Vendor" },
  { id: "PhoneNumber1", label: "PhoneNumber1", minWidth: 150, group: "Vendor" },
  {
    id: "FlatUnitNumber1",
    label: "FlatUnitNumber1",
    minWidth: 150,
    group: "Vendor",
  },
  {
    id: "StreetNumber1",
    label: "StreetNumber1",
    minWidth: 150,
    group: "Vendor",
  },
  { id: "StreetName1", label: "StreetName1", minWidth: 150, group: "Vendor" },
  { id: "StreetType1", label: "StreetType1", minWidth: 150, group: "Vendor" },
  { id: "Suburb1", label: "Suburb1", minWidth: 150, group: "Vendor" },
  { id: "Postcode1", label: "Postcode1", minWidth: 150, group: "Vendor" },
  { id: "State1", label: "State1", minWidth: 150, group: "Vendor" },
  { id: "Country1", label: "Country1", minWidth: 150, group: "Vendor" },

  // Vendor Future
  {
    id: "FlatUnitNumber2",
    label: "FlatUnitNumber2",
    minWidth: 150,
    group: "VendorFuture",
  },
  {
    id: "StreetNumber2",
    label: "StreetNumber2",
    minWidth: 150,
    group: "VendorFuture",
  },
  {
    id: "StreetName2",
    label: "StreetName2",
    minWidth: 150,
    group: "VendorFuture",
  },
  {
    id: "StreetType2",
    label: "StreetType2",
    minWidth: 150,
    group: "VendorFuture",
  },
  { id: "Suburb2", label: "Suburb2", minWidth: 150, group: "VendorFuture" },
  { id: "Postcode2", label: "Postcode2", minWidth: 150, group: "VendorFuture" },
  { id: "State2", label: "State2", minWidth: 150, group: "VendorFuture" },
  { id: "Country2", label: "Country2", minWidth: 150, group: "VendorFuture" },

  // Purchaser
  {
    id: "CompanyName2",
    label: "CompanyName2",
    minWidth: 150,
    group: "Purchaser",
  },
  { id: "ACNNumber2", label: "ACNNumber2", minWidth: 150, group: "Purchaser" },
  { id: "Surname2", label: "Surname2", minWidth: 150, group: "Purchaser" },
  {
    id: "GivenNames2",
    label: "GivenNames2",
    minWidth: 150,
    group: "Purchaser",
  },
  { id: "Email2", label: "Email2", minWidth: 150, group: "Purchaser" },
  {
    id: "PhoneNumber2",
    label: "PhoneNumber2",
    minWidth: 150,
    group: "Purchaser",
  },
  {
    id: "DateOfBirth2",
    label: "DateOfBirth2",
    minWidth: 150,
    group: "Purchaser",
  },
  {
    id: "FlatUnitNumber3",
    label: "FlatUnitNumber3",
    minWidth: 150,
    group: "Purchaser",
  },
  {
    id: "StreetNumber3",
    label: "StreetNumber3",
    minWidth: 150,
    group: "Purchaser",
  },
  {
    id: "StreetName3",
    label: "StreetName3",
    minWidth: 150,
    group: "Purchaser",
  },
  {
    id: "StreetType3",
    label: "StreetType3",
    minWidth: 150,
    group: "Purchaser",
  },
  { id: "Suburb3", label: "Suburb3", minWidth: 150, group: "Purchaser" },
  { id: "Postcode3", label: "Postcode3", minWidth: 150, group: "Purchaser" },
  { id: "State3", label: "State3", minWidth: 150, group: "Purchaser" },
  { id: "Country3", label: "Country3", minWidth: 150, group: "Purchaser" },

  // Purchaser Future
  {
    id: "FlatUnitNumber4",
    label: "FlatUnitNumber4",
    minWidth: 150,
    group: "PurchaserFuture",
  },
  {
    id: "StreetNumber4",
    label: "StreetNumber4",
    minWidth: 150,
    group: "PurchaserFuture",
  },
  {
    id: "StreetName4",
    label: "StreetName4",
    minWidth: 150,
    group: "PurchaserFuture",
  },
  {
    id: "StreetType4",
    label: "StreetType4",
    minWidth: 150,
    group: "PurchaserFuture",
  },
  { id: "Suburb4", label: "Suburb4", minWidth: 150, group: "PurchaserFuture" },
  {
    id: "Postcode4",
    label: "Postcode4",
    minWidth: 150,
    group: "PurchaserFuture",
  },
  { id: "State4", label: "State4", minWidth: 150, group: "PurchaserFuture" },
  {
    id: "Country4",
    label: "Country4",
    minWidth: 150,
    group: "PurchaserFuture",
  },
  {
    id: "PrincipalPlaceOfResidence",
    label: "PrincipalPlaceOfResidence",
    minWidth: 150,
    group: "PurchaserFuture",
  },
];
