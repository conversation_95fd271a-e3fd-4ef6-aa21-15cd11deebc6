export const yvwColumns: any[] = [
  // General Information
  { id: "Image", label: "Images", minWidth: 150, group: "GeneralInformation" }, // Matches sewColumns "Image"
  { id: "DocumentType", label: "DocumentType", minWidth: 150, group: "GeneralInformation" }, // Matches sewColumns "DocumentType"

  // Property Address - DisplayAddress
  { id: "FlatUnitNumber", label: "ns1:UnitNumber30", minWidth: 100, group: "PropertyAddress:DisplayAddress" }, // Matches sewColumns "FlatUnitNumber"
  { id: "StreetName", label: "ns1:StreetLocation", minWidth: 150, group: "PropertyAddress:DisplayAddress" }, // Maps to StreetName (display address)
  { id: "StreetName", label: "ns1:StreetName", minWidth: 150, group: "PropertyAddress:DisplayAddress" }, // Matches sewColumns "StreetName"
  { id: "Suburb", label: "ns1:Suburb", minWidth: 100, group: "PropertyAddress:DisplayAddress" }, // Matches sewColumns "Suburb"
  { id: "Postcode", label: "ns1:Postcode", minWidth: 80, group: "PropertyAddress:DisplayAddress" }, // Matches sewColumns "Postcode"

  // Property Address - ParsedAddress
  { id: "FlatUnitNumber", label: "ns1:UnitNumber40", minWidth: 100, group: "PropertyAddress:ParsedAddress" }, // Matches sewColumns "FlatUnitNumber"
  { id: "StreetNumber", label: "ns1:StreetNumber1", minWidth: 100, group: "PropertyAddress:ParsedAddress" }, // Matches sewColumns "StreetNumber"
  { id: "StreetName", label: "ns1:Street", minWidth: 150, group: "PropertyAddress:ParsedAddress" }, // Matches sewColumns "StreetName"
  { id: "StreetType", label: "ns1:StreetType", minWidth: 100, group: "PropertyAddress:ParsedAddress" }, // Matches sewColumns "StreetType"
  { id: "Suburb", label: "ns1:Suburb2", minWidth: 100, group: "PropertyAddress:ParsedAddress" }, // Matches sewColumns "Suburb"
  { id: "Postcode", label: "ns1:Postcode3", minWidth: 80, group: "PropertyAddress:ParsedAddress" }, // Matches sewColumns "Postcode"

  // Property Address - Titles
  { id: "", label: "ns1:VolumeFolio", minWidth: 120, group: "PropertyAddress:Titles" }, // No direct match in sewColumns

  // Property Address - LotPlansAndCrownAllotments
  { id: "LotNumber", label: "ns1:Lots", minWidth: 80, group: "PropertyAddress:LotPlansAndCrownAllotments" }, // Matches sewColumns "LotNumber"
  { id: "PlanNumber", label: "ns1:PlanNumber", minWidth: 100, group: "PropertyAddress:LotPlansAndCrownAllotments" }, // Matches sewColumns "PlanNumber"

  // Transferor - VendorDetails
  { id: "", label: "ns1:CustomerType", minWidth: 120, group: "Transferor:VendorDetails" }, // No direct match in sewColumns
  { id: "CompanyName1", label: "ns1:BusinessName", minWidth: 150, group: "Transferor:VendorDetails" }, // Matches sewColumns "CompanyName1"
  { id: "ACNNumber1", label: "ns1:ACNNumber", minWidth: 120, group: "Transferor:VendorDetails" }, // Matches sewColumns "ACNNumber1"

  // Transferor - Vendor
  { id: "", label: "ns1:Title", minWidth: 80, group: "Transferor:Vendor" }, // No direct match in sewColumns
  { id: "Surname1", label: "ns1:Surname", minWidth: 120, group: "Transferor:Vendor" }, // Matches sewColumns "Surname1"
  { id: "GivenNames1", label: "ns1:GivenNames", minWidth: 120, group: "Transferor:Vendor" }, // Matches sewColumns "GivenNames1"
  { id: "PhoneNumber1", label: "ns1:Phone16", minWidth: 100, group: "Transferor:Vendor" }, // Matches sewColumns "PhoneNumber1"
  { id: "Email1", label: "ns1:Email16", minWidth: 150, group: "Transferor:Vendor" }, // Matches sewColumns "Email1"
  { id: "FlatUnitNumber1", label: "ns1:UnitNumber", minWidth: 100, group: "Transferor:Vendor" }, // Matches sewColumns "FlatUnitNumber1"
  { id: "StreetNumber1", label: "ns1:StreetNumber", minWidth: 100, group: "Transferor:Vendor" }, // Matches sewColumns "StreetNumber1"
  { id: "StreetName1", label: "ns1:Street4", minWidth: 150, group: "Transferor:Vendor" }, // Matches sewColumns "StreetName1"
  { id: "StreetType1", label: "ns1:StreetType5", minWidth: 100, group: "Transferor:Vendor" }, // Matches sewColumns "StreetType1"
  { id: "Suburb1", label: "ns1:Suburb6", minWidth: 100, group: "Transferor:Vendor" }, // Matches sewColumns "Suburb1"
  { id: "Postcode1", label: "ns1:Postcode7", minWidth: 80, group: "Transferor:Vendor" }, // Matches sewColumns "Postcode1"
  { id: "State1", label: "ns1:State", minWidth: 80, group: "Transferor:Vendor" }, // Matches sewColumns "State1"
  { id: "Country1", label: "ns1:Country", minWidth: 100, group: "Transferor:Vendor" }, // Matches sewColumns "Country1"
  { id: "", label: "ns1:DeliveryPointIdentifier", minWidth: 120, group: "Transferor:Vendor" }, // No direct match in sewColumns

  // Transferor - Vendor:FutureNoticesAddress
  { id: "FlatUnitNumber2", label: "ns1:UnitNumber2", minWidth: 100, group: "Transferor:Vendor:FutureNoticesAddress" }, // Matches sewColumns "FlatUnitNumber2"
  { id: "StreetNumber2", label: "ns1:StreetNumber8", minWidth: 100, group: "Transferor:Vendor:FutureNoticesAddress" }, // Matches sewColumns "StreetNumber2"
  { id: "StreetName2", label: "ns1:Street9", minWidth: 150, group: "Transferor:Vendor:FutureNoticesAddress" }, // Matches sevColumns "StreetName2"
  { id: "StreetType2", label: "ns1:StreetType10", minWidth: 100, group: "Transferor:Vendor:FutureNoticesAddress" }, // Matches sewColumns "StreetType2"
  { id: "Suburb2", label: "ns1:Suburb11", minWidth: 100, group: "Transferor:Vendor:FutureNoticesAddress" }, // Matches sewColumns "Suburb2"
  { id: "Postcode2", label: "ns1:Postcode12", minWidth: 80, group: "Transferor:Vendor:FutureNoticesAddress" }, // Matches sewColumns "Postcode2"
  { id: "State2", label: "ns1:State13", minWidth: 80, group: "Transferor:Vendor:FutureNoticesAddress" }, // Matches sewColumns "State2"
  { id: "Country2", label: "ns1:Country14", minWidth: 100, group: "Transferor:Vendor:FutureNoticesAddress" }, // Matches sewColumns "Country2"
  { id: "", label: "ns1:DeliveryPointIdentifier15", minWidth: 120, group: "Transferor:Vendor:FutureNoticesAddress" }, // No direct match in sewColumns

  // Transferee - PurchaserDetails
  { id: "", label: "ns1:CustomerType16", minWidth: 120, group: "Transferee:PurchaserDetails" }, // No direct match in sewColumns
  { id: "CompanyName2", label: "ns1:BusinessName2", minWidth: 150, group: "Transferee:PurchaserDetails" }, // Matches sewColumns "CompanyName2"
  { id: "ACNNumber2", label: "ns1:ACNNumber2", minWidth: 120, group: "Transferee:PurchaserDetails" }, // Matches sewColumns "ACNNumber2"

  // Transferee - Purchaser
  { id: "", label: "ns1:Title17", minWidth: 80, group: "Transferee:Purchaser" }, // No direct match in sewColumns
  { id: "Surname2", label: "ns1:Surname18", minWidth: 120, group: "Transferee:Purchaser" }, // Matches sewColumns "Surname2"
  { id: "GivenNames2", label: "ns1:GivenNames19", minWidth: 120, group: "Transferee:Purchaser" }, // Matches sewColumns "GivenNames2"
  { id: "DateOfBirth2", label: "ns1:DOB", minWidth: 100, group: "Trfansferee:Purchaser" }, // Matches sewColumns "DateOfBirth2"
  { id: "PhoneNumber2", label: "ns1:Phone17", minWidth: 100, group: "Transferee:Purchaser" }, // Matches sewColumns "PhoneNumber2"
  { id: "Email2", label: "ns1:Email17", minWidth: 150, group: "Transferee:Purchaser" }, // Matches sewColumns "Email2"
  { id: "FlatUnitNumber3", label: "ns1:UnitNumber20", minWidth: 100, group: "Transferee:Purchaser" }, // Matches sewColumns "FlatUnitNumber3"
  { id: "StreetNumber3", label: "ns1:StreetNumber21", minWidth: 100, group: "Transferee:Purchaser" }, // Matches sewColumns "StreetNumber3"
  { id: "StreetName3", label: "ns1:Street22", minWidth: 150, group: "Transferee:Purchaser" }, // Matches sewColumns "StreetName3"
  { id: "StreetType3", label: "ns1:StreetType23", minWidth: 100, group: "Transferee:Purchaser" }, // Matches sewColumns "StreetType3"
  { id: "Suburb3", label: "ns1:Suburb24", minWidth: 100, group: "Transferee:Purchaser" }, // Matches sewColumns "Suburb3"
  { id: "Postcode3", label: "ns1:Postcode25", minWidth: 80, group: "Transferee:Purchaser" }, // Matches sewColumns "Postcode3"
  { aarde: "State3", label: "ns1:State26", minWidth: 80, group: "Transferee:Purchaser" }, // Matches sewColumns "State3"
  { id: "Country3", label: "ns1:Country27", minWidth: 100, group: "Transferee:Purchaser" }, // Matches sewColumns "Country3"
  { id: "", label: "ns1:DeliveryPointIdentifier28", minWidth: 120, group: "Transferee:Purchaser" }, // No direct match in sewColumns

  // Transferee - Purchaser:FutureNoticesAddress
  { id: "FlatUnitNumber4", label: "ns1:UnitNumber29", minWidth: 100, group: "Transferee:Purchaser:FutureNoticesAddress" }, // Matches sewColumns "FlatUnitNumber4"
  { id: "StreetNumber4", label: "ns1:StreetNumber30", minWidth: 100, group: "Transferee:Purchaser:FutureNoticesAddress" }, // Matches sewColumns "StreetNumber4"
  { id: "StreetName4", label: "ns1:Street31", minWidth: 150, group: "Transferee:Purchaser:FutureNoticesAddress" }, // Matches sewColumns "StreetName4"
  { id: "StreetType4", label: "ns1:StreetType32", minWidth: 100, group: "Transferee:Purchaser:FutureNoticesAddress" }, // Matches sewColumns "StreetType4"
  { id: "Suburb4", label: "ns1:Suburb33", minWidth: 100, group: "Transferee:Purchaser:FutureNoticesAddress" }, // Matches sewColumns "Suburb4"
  { id: "Postcode4", label: "ns1:Postcode34", minWidth: 80, group: "Transferee:Purchaser:FutureNoticesAddress" }, // Matches sewColumns "Postcode4"
  { id: "State4", label: "ns1:State35", minWidth: 80, group: "Transferee:Purchaser:FutureNoticesAddress" }, // Matches sewColumns "State4"
  { id: "Country4", label: "ns1:Country36", minWidth: 100, group: "Transferee:Purchaser:FutureNoticesAddress" }, // Matches sewColumns "Country4"
  { id: "", label: "ns1:DeliveryPointIdentifier37", minWidth: 120, group: "Transferee:Purchaser:FutureNoticesAddress" }, // No direct match in sewColumns

  // Transaction Details
  { id: "", label: "ns1:DatePossessionGiven", minWidth: 120, group: "TransactionDetails" }, // No direct match in sewColumns
  { id: "", label: "ns1:ChangeOfOwnerType", minWidth: 150, group: "TransactionDetails" }, // No direct match in sewColumns
  { id: "DateOfTransfer", label: "ns1:TransferType", minWidth: 120, group: "TransactionDetails" }, // Matches sewColumns "DateOfTransfer"

  // Solicitor/Agent Details - VendorSolicitorAgentDetails (Transferor)
  { id: "", label: "ns1:Name", minWidth: 150, group: "VendorSolicitorAgentDetails:Transferor" }, // No direct match in sewColumns
  { id: "", label: "ns1:Phone", minWidth: 100, group: "VendorSolicitorAgentDetails:Transferor" }, // No direct match in sewColumns
  { id: "", label: "ns1:Email18", minWidth: 150, group: "VendorSolicitorAgentDetails:Transferor" }, // No direct match in sewColumns
  { id: "", label: "ns1:LegalCertification", minWidth: 150, group: "VendorSolicitorAgentDetails:Transferor" }, // No direct match in sewColumns

  // Solicitor/Agent Details - PurchaserSolicitorAgentDetails (Transferee)
  { id: "", label: "ns1:Name38", minWidth: 150, group: "PurchaserSolicitorAgentDetails:Transferee" }, // No direct match in sewColumns
  { id: "", label: "ns1:Phone39", minWidth: 100, group: "PurchaserSolicitorAgentDetails:Transferee" }, // No direct match in sewColumns
  { id: "", label: "ns1:Email19", minWidth: 150, group: "PurchaserSolicitorAgentDetails:Transferee" }, // No direct match in sewColumns
  { id: "", label: "ns1:LegalCertification40", minWidth: 150, group: "PurchaserSolicitorAgentDetails:Transferee" }, // No direct match in sewColumns

  // CertificateName
  { id: "", label: "ns2:CertificateName", minWidth: 150, group: "CertificateName" }, // No direct match in sewColumns
];