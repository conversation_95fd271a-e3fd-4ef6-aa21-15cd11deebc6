import * as React from "react";
import { Box, TextField, Typo<PERSON>, Button } from "@mui/material";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TablePagination from "@mui/material/TablePagination";
import TableRow from "@mui/material/TableRow";
import SaveIcon from "@mui/icons-material/Save";
import CancelIcon from "@mui/icons-material/Cancel";
import CircularProgress from "@mui/material/CircularProgress";
import { useExtracteDataHooks } from "@/hooks/useExtracteData";
import { Column, sewColumns } from "./helper-component/sewColumn";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { yvwColumns } from "./helper-component/yvwColumn";

const ExtractedDataDetails = ({ id, job_type }: any) => {
  const {
    extractedIdData,
    extractedPopupLoading,
    handleDownloadCSV,
    rows,
    page,
    rowsPerPage,
    editingRow,
    editedRowData,
    handleRowClick,
    handleCellChange,
    handleSave,
    handleCancel,
    handleChangePage,
    handleChangeRowsPerPage,
    currentFileIndex,
    totalFiles,
    handleNextFile,
    handlePrevFile,
  } = useExtracteDataHooks(id);

  const getColumnsByJobType = (jobType: string): Column[] => {
    switch (jobType) {
      case "SEW":
        return sewColumns;
      case "YVW":
        return yvwColumns;
      default:
        return sewColumns;
    }
  };

  const columns = getColumnsByJobType(job_type);
  const columnGroups = columns.reduce(
    (acc: Record<string, Column[]>, column) => {
      if (!acc[column.group]) acc[column.group] = [];
      acc[column.group].push(column);
      return acc;
    },
    {}
  );

  const [fileurl, setFileurl] = React.useState<string | null>(null);
  console.log("fileurl", fileurl);

  React.useEffect(() => {
    const fileName = extractedIdData?.files?.[currentFileIndex]?.fileName;
    console.log(fileName);
    
    if (fileName) {
      setFileurl(`https://suninfotek-docx.s3.ap-south-1.amazonaws.com/${fileName}`);
    } else {
      setFileurl(null);
    }
  }, [extractedIdData, currentFileIndex]);

  if (extractedPopupLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
          width: "100%",
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 2,
          }}
        >
          <CircularProgress />
          <Typography variant="h6">Loading data...</Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, display: "flex", flexDirection: "column", gap: 3 }}>
      {/* Document Preview Section */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
          gap: 2,
          mt: 2,
        }}
      >
        <Button
          variant="contained"
          onClick={handleDownloadCSV}
          sx={{ borderRadius: "6px", minWidth: "150px" }}
        >
          Download CSV
        </Button>

        <Button
          variant="contained"
          onClick={handlePrevFile}
          disabled={currentFileIndex === 0}
          startIcon={<ArrowBackIosIcon />}
          sx={{ borderRadius: "6px", minWidth: "150px" }}
        >
          Previous
        </Button>

        <Typography variant="body2" sx={{ mx: 1 }}>
          File {currentFileIndex + 1} of {totalFiles}
        </Typography>

        <Button
          variant="contained"
          onClick={handleNextFile}
          disabled={currentFileIndex === totalFiles - 1}
          endIcon={<ArrowForwardIosIcon />}
          sx={{ borderRadius: "6px", minWidth: "150px" }}
        >
          Next
        </Button>
      </Box>

      {/* Document Preview Section */}
      {fileurl && (
        <Paper elevation={3} sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Document Preview:{" "}
            {extractedIdData?.files?.[currentFileIndex]?.fileName}
          </Typography>
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              flexDirection: "column",
              gap: 2,
              border: "1px solid #e0e0e0",
              borderRadius: 1,
              p: 2,
              backgroundColor: "#f9f9f9",
            }}
          >
            <iframe
              src={`https://docs.google.com/viewer?url=${encodeURIComponent(
                fileurl
              )}&embedded=true`}
              width="100%"
              height="400px"
              frameBorder="0"
            />
          </Box>
        </Paper>
      )}

      {/* Extracted Data Table Section */}
      <Paper elevation={3} sx={{ p: 2 }}>
        <Box
          sx={{ display: "flex", justifyContent: "flex-end", gap: 1, mb: 2 }}
        >
          {editingRow !== null && (
            <>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSave}
                sx={{
                  borderRadius: "4px",
                  backgroundColor: "success.main",
                  "&:hover": { backgroundColor: "success.dark" },
                }}
              >
                Save
              </Button>
              <Button
                variant="contained"
                startIcon={<CancelIcon />}
                onClick={handleCancel}
                sx={{
                  borderRadius: "4px",
                  backgroundColor: "error.main",
                  "&:hover": { backgroundColor: "error.dark" },
                }}
              >
                Cancel
              </Button>
            </>
          )}
        </Box>

        <TableContainer sx={{ maxHeight: 600, overflowY: "auto" }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                {Object.entries(columnGroups).map(([groupName, groupCols]) => (
                  <TableCell
                    key={groupName}
                    align="center"
                    colSpan={groupCols.length}
                    sx={{
                      fontWeight: "bold",
                      backgroundColor: "#f5f5f5",
                      borderRight: "1px solid #e0e0e0",
                    }}
                  >
                    {groupName}
                  </TableCell>
                ))}
              </TableRow>
              <TableRow>
                {columns.map((column) => (
                  <TableCell
                    key={column.id}
                    align={column.align}
                    style={{
                      minWidth: column.minWidth,
                      backgroundColor: "#f9f9f9",
                      fontWeight: "bold",
                      borderRight: "1px solid #e0e0e0",
                    }}
                  >
                    {column.label}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {rows
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row: any, rowIndex: number) => (
                  <TableRow
                    hover
                    key={rowIndex}
                    sx={{
                      cursor: "pointer",
                      backgroundColor:
                        editingRow === rowIndex ? "#f5f5f5" : "inherit",
                    }}
                  >
                    {columns.map((column) => (
                      <TableCell
                        key={column.id}
                        align={column.align}
                        sx={{
                          borderRight: "1px solid #f0f0f0",
                          padding: "8px",
                          whiteSpace: "nowrap",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                        }}
                        onClick={() => handleRowClick(rowIndex)}
                      >
                        {editingRow === rowIndex ? (
                          <TextField
                            variant="standard"
                            value={editedRowData?.[column.id] || ""}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => handleCellChange(e, column.id)}
                            fullWidth
                            InputProps={{
                              disableUnderline: true,
                              style: { fontSize: "inherit" },
                            }}
                          />
                        ) : (
                          row[column.id] || ""
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[10, 25, 100]}
          component="div"
          count={rows.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
    </Box>
  );
};

export default ExtractedDataDetails;
