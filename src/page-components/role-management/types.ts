export type RoleManagementProps = {
  id?: number;
  name: string;
  email: string;
  phone: string;
  password: string;
};

export type AddEditRoleManagementProps = {
  selectedRoles?: RoleManagementProps | null;
  closeModal: () => void;
  handleFormSubmit: (data: RoleManagementProps) => void;
};

export type RowsProps = {
  id: number;
  name: string;
  description: string;
  updated_at: string;
};
