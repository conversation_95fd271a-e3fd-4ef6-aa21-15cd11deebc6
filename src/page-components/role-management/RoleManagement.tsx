"use client";
import UIDataTable from "@/ui-components/table";
import Grid from "@mui/material/Grid";
import { GridColDef } from "@mui/x-data-grid";
import ActionMenu from "@/ui-components/table/ActionMenu";
import { useMemo } from "react";
import { UIDataTableProps } from "@/ui-components/table/types";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import AppDialog from "@/ui-components/dialog/Dialog";
import { DialogMaxWidthProps } from "@/ui-components/dialog/types";
import { useRoleManagement } from "@/hooks/useRoleMangement";
import AddEditRoleManagement from "./helper-component/Add-EditRoleMangement";
import { LinearProgress } from "@mui/material";
import { DeleteRole } from "./helper-component/DeleteRole";

const RoleManagementComponent = () => {
  const {
    state: { searchText, page, pageSize, isFormModalOpen, selectedRoles },
    roleUsersData,
    roleDataLoading,
    handleSearchInputChange,
    handlePageChange,
    handlePageSizeChange,
    onFormModalHandle,
    handleFormSubmit,
  } = useRoleManagement();

  const actions = [
    {
      id: 1,
      icon: "ic:baseline-edit",
      title: "Edit",
      onItemClick: (role: string) => {
        onFormModalHandle("edit", role);
      },
    },
    {
      id: 3,
      icon: "ic:baseline-delete-outline",
      title: "Delete",
      onItemClick: (role: string) => {
        onFormModalHandle("delete", role);
      },
    },
  ];

  const columns: GridColDef[] = [
    {
      field: "__check__",
      headerName: "",
      width: 52,
      sortable: false,
      disableColumnMenu: true,
    },
    {
      field: "id",
      headerName: "UserId",
      editable: false,
      align: "left",
      flex: 1,
      minWidth: 50,
      disableColumnMenu: true,
    },
    {
      field: "name",
      headerName: "USER NAME",
      editable: false,
      align: "left",
      flex: 1,
      minWidth: 230,
      disableColumnMenu: true,
    },
    {
      field: "email",
      headerName: "Email",
      headerAlign: "left",
      align: "left",
      editable: false,
      flex: 1,
      minWidth: 150,
      disableColumnMenu: true,
    },
    {
      field: "phone",
      headerName: "PHONE",
      headerAlign: "left",
      editable: false,
      flex: 1,
      minWidth: 120,
      disableColumnMenu: true,
    },
    {
      field: "password",
      headerName: "PASSWORD",
      headerAlign: "left",
      editable: false,
      flex: 1,
      minWidth: 120,
      disableColumnMenu: true,
    },
    {
      field: "action",
      headerAlign: "right",
      headerName: "ACTION",
      align: "right",
      editable: false,
      sortable: false,
      flex: 1,
      minWidth: 90,
      disableColumnMenu: true,
      renderCell: (params) => (
        <ActionMenu actions={actions} params={params.row} />
      ),
    },
  ];

  const ToolbarItems = useMemo(
    () => (
      <>
        <IconifyIcon
          icon="mdi:folder-add"
          sx={{ fontSize: "20px" }}
          onClick={() => onFormModalHandle("add")}
        />
      </>
    ),
    [onFormModalHandle]
  );

  const TableDataProps = useMemo(
    () => ({
      tableData: roleUsersData?.data,
      totalPage: roleUsersData?.pagination?.totalPages,
      page,
      pageSize,
      handlePageChange,
      handlePageSizeChange,
    }),
    [page, pageSize, roleUsersData, handlePageChange, handlePageSizeChange]
  );

  const TableProps: UIDataTableProps = useMemo(
    () => ({
      tableName: "Client",
      columns,
      searchText,
      handleSearchInputChange,
      tableDataProps: TableDataProps,
      ToolbarItems,
      handlePageChange,
      handlePageSizeChange,
      refetch: "",
    }),
    [
      columns,
      searchText,
      handleSearchInputChange,
      TableDataProps,
      ToolbarItems,
      handlePageChange,
      handlePageSizeChange,
    ]
  );

  const DialogMap: Record<string, React.ReactNode> = {
    add: (
      <AddEditRoleManagement
        closeModal={() => onFormModalHandle()}
        handleFormSubmit={handleFormSubmit}
      />
    ),
    edit: (
      <AddEditRoleManagement
        closeModal={() => onFormModalHandle()}
        handleFormSubmit={handleFormSubmit}
        selectedRoles={selectedRoles?.roleManagement}
      />
    ),
    delete: (
      <DeleteRole
        selectedRoles={selectedRoles}
        onClose={() => onFormModalHandle()}
      />
    ),
  };

  if (roleDataLoading) {
    return <LinearProgress />;
  }

  const DialogTitleMap: Record<string, string> = {
    add: "Add Client",
    edit: "Edit Client",
    delete: "Delete Client",
  };

  const dialogType = selectedRoles?.type || "";
  const DialogComponent = DialogMap[dialogType] || null;
  const DialogTitleText = DialogTitleMap[dialogType] || "Client Data";

  const DialogProps = {
    title: DialogTitleText,
    open: isFormModalOpen,
    handleClose: () => onFormModalHandle(),
    body: DialogComponent,
    maxWidth: "md" as DialogMaxWidthProps,
  };

  return (
    <>
      <Grid>
        <UIDataTable {...TableProps} />
      </Grid>
      <AppDialog {...DialogProps} />
    </>
  );
};

export default RoleManagementComponent;
