"use client";
import { useCallback, useEffect, useState } from "react";
import FormLayout from "@/ui-components/form/layout/FormLayout";
import { useForm } from "react-hook-form";
import AppButton from "@/ui-components/button/Button";
import { AddEditRoleManagementProps, RoleManagementProps } from "../types";
import TextInput from "@/ui-components/form/hooks/TextInput";
import { zodResolver } from "@hookform/resolvers/zod";
import { RoleManagementSchema } from "@/lib/roleMangement";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import { InputAdornment, IconButton } from "@mui/material";

const defaultValues = {
  name: "",
  email: "",
  phone: "",
  password: "",
};

const AddEditRoleManagement = ({
  closeModal,
  handleFormSubmit,
  selectedRoles,
}: AddEditRoleManagementProps) => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<RoleManagementProps>({
    resolver: zodResolver(RoleManagementSchema),
    defaultValues,
  });

  useEffect(() => {
    if (selectedRoles) {
      reset({
        id: selectedRoles.id,
        name: selectedRoles.name || "",
        email: selectedRoles.email || "",
        phone: selectedRoles.phone || "",
        // password: selectedRoles.password || "",
      });
    } else {
      reset();
    }
  }, [selectedRoles, reset]);

  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const handleFormSubmitWrapper = useCallback(
    (data: RoleManagementProps) => {
      const transformedData = {
        ...data,
        id: selectedRoles?.id,
      };
      handleFormSubmit(transformedData);
      closeModal();
    },
    [handleFormSubmit, closeModal, selectedRoles]
  );

  return (
    <form onSubmit={handleSubmit(handleFormSubmitWrapper)}>
      <FormLayout>
        <TextInput
          name="name"
          control={control}
          label="User Name *"
          helperText={errors.name?.message}
        />
        <TextInput
          name="email"
          control={control}
          label="Email *"
          helperText={errors.email?.message}
        />
        <TextInput
          name="password"
          control={control}
          label="Password *"
          helperText={errors.password?.message}
          type={showPassword ? "text" : "password"}
          endAdornment={
            <InputAdornment position="end">
              <IconButton
                aria-label="toggle password visibility"
                onClick={togglePasswordVisibility}
                edge="end"
              >
                <IconifyIcon
                  icon={
                    showPassword
                      ? "ic:outline-visibility"
                      : "ic:outline-visibility-off"
                  }
                  color="neutral.main"
                />
              </IconButton>
            </InputAdornment>
          }
        />
        <TextInput
          name="phone"
          control={control}
          label="Phone *"
          helperText={errors.phone?.message}
        />
      </FormLayout>
      <AppButton type="submit" disabled={isSubmitting}>
        Save
      </AppButton>
      <AppButton type="button" onClick={closeModal} disabled={isSubmitting}>
        Cancel
      </AppButton>
    </form>
  );
};

export default AddEditRoleManagement;
