"use client";
import UIDataTable from "@/ui-components/table";
import Grid from "@mui/material/Grid";
import { GridColDef } from "@mui/x-data-grid";
import ActionMenu from "@/ui-components/table/ActionMenu";
import { useMemo } from "react";
import { UIDataTableProps } from "@/ui-components/table/types";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import AppDialog from "@/ui-components/dialog/Dialog";
import { Box, LinearProgress, Typography } from "@mui/material";
import Refresh from "@/core-components/loading/Refresh";
import { DialogMaxWidthProps } from "@/ui-components/dialog/types";
import ExportDropdown from "@/core-components/exportDropdown/ExportDropdown";
import AddEditTemplate from "./helper-component/AddEditTemplate";
import { useTemplete } from "@/hooks/useTemplete";
import { DeleteTemplate } from "./helper-component/DeleteTemplate";

const TemplatesPage = () => {
  const {
    state: { searchText, page, pageSize, isFormModalOpen, selectedTemplate },
    templateData,
    templateLoading,
    handleSearchInputChange,
    handlePageChange,
    handlePageSizeChange,
    onFormModalHandle,
    handleFormSubmit,
    handleImageUpload,
    handleSortModelChange,
    handleFormSubmitWrapper,
  } = useTemplete();

  const actions = [
    // {
    //   id: 1,
    //   icon: "ic:baseline-edit",
    //   title: "Edit",
    //   onItemClick: (template: any) => {
    //     onFormModalHandle("edit", template);
    //   },
    // },

    {
      id: 2,
      icon: "ic:baseline-remove-red-eye",
      title: "View",
      onItemClick: (template: any) => {
        onFormModalHandle("view", template);
      },
    },
    {
      id: 3,
      icon: "ic:baseline-delete-outline",
      title: "Delete",
      onItemClick: (template: any) => {
        onFormModalHandle("delete", template);
      },
    },
  ];

  const columns: GridColDef[] = [
    {
      field: "__check__",
      headerName: "",
      width: 52,
      sortable: false,
      disableColumnMenu: true,
    },
    {
      field: "id",
      headerName: "ID",
      editable: false,
      align: "left",
      flex: 1,
      minWidth: 50,
      disableColumnMenu: true,
    },
    {
      field: "name",
      headerName: "TEMPLATE NAME",
      editable: false,
      align: "left",
      flex: 1,
      minWidth: 230,
      disableColumnMenu: true,
    },
    {
      field: "",
      headerName: "USER NAME",
      headerAlign: "left",
      align: "left",
      editable: false,
      flex: 1,
      minWidth: 150,
      disableColumnMenu: true,
      renderCell: (params) => {
        const userName = params?.row?.created_by?.name;

        return (
          <Typography variant="body2" fontWeight={600} sx={{ marginTop: 2 }}>
            {userName || "-"}
          </Typography>
        );
      },
    },

    {
      field: "created_at",
      headerName: "CREATED DATE",
      headerAlign: "left",
      editable: false,
      flex: 1,
      minWidth: 120,
      disableColumnMenu: true,
    },
    {
      field: "view",
      headerName: "VIEW JSON",
      headerAlign: "center",
      align: "center",
      editable: false,
      flex: 1,
      minWidth: 120,
      disableColumnMenu: true,
      renderCell: (params) => {
        return (
          <IconifyIcon
            icon="ic:baseline-remove-red-eye"
            sx={{ fontSize: "20px", cursor: "pointer" }}
            onClick={() => onFormModalHandle("view", params.row)}
          />
        );
      },
    },
    {
      field: "action",
      headerAlign: "right",
      headerName: "ACTION",
      align: "right",
      editable: false,
      sortable: false,
      flex: 1,
      minWidth: 90,
      disableColumnMenu: true,
      renderCell: (params) => (
        <ActionMenu actions={actions} params={params.row} />
      ),
    },
  ];

  const ToolbarItems = useMemo(
    () => (
      <>
        {/* <IconifyIcon
          icon="mdi:folder-add"
          sx={{ fontSize: "20px" }}
          onClick={() => onFormModalHandle("add")}
        /> */}
      </>
    ),
    [onFormModalHandle]
  );

  const TableDataProps = useMemo(
    () => ({
      tableData: templateData?.data,
      totalPage: templateData?.pagination?.totalPages,
      page,
      pageSize,
      handleSortModelChange,
    }),
    [templateData, page, pageSize, handleSortModelChange]
  );

  const TableProps: UIDataTableProps = useMemo(
    () => ({
      tableName: "Template Files",
      columns,
      searchText,
      handleSearchInputChange,
      tableDataProps: TableDataProps,
      ToolbarItems,
      handlePageChange,
      handlePageSizeChange,
      refetch: "",
    }),
    [
      columns,
      searchText,
      handleSearchInputChange,
      TableDataProps,
      ToolbarItems,
      handlePageChange,
      handlePageSizeChange,
    ]
  );

  if (templateLoading) {
    return <LinearProgress />;
  }

  const DialogMap: Record<string, React.ReactNode> = {
    add: (
      <AddEditTemplate
        closeModal={() => onFormModalHandle()}
        handleFormSubmit={handleFormSubmit}
        handleImageUpload={handleImageUpload}
        handleFormSubmitWrapper={handleFormSubmitWrapper}
        selectedTemplate={selectedTemplate?.templete}
      />
    ),
    edit: (
      <AddEditTemplate
        closeModal={() => onFormModalHandle()}
        handleFormSubmit={handleFormSubmit}
        handleImageUpload={handleImageUpload}
        handleFormSubmitWrapper={handleFormSubmitWrapper}
        selectedTemplate={selectedTemplate?.templete}
      />
    ),
    view: selectedTemplate ? (
      <pre>
        {JSON.stringify(
          JSON.parse(selectedTemplate?.templete?.config_json || "{}"),
          null,
          2
        )}
      </pre>
    ) : null,
    delete: (
      <DeleteTemplate
        selectedTemplate={selectedTemplate}
        onClose={() => onFormModalHandle()}
      />
    ),
  };

  const DialogTitleMap: Record<string, string> = {
    add: "Add Template",
    edit: "Edit Template",
    view: "View Template",
  };

  const dialogType = selectedTemplate?.type || "";
  const DialogComponent = DialogMap[dialogType] || null;
  const DialogTitleText = DialogTitleMap[dialogType] || "Add Upload Files";

  const DialogProps = {
    title: DialogTitleText,
    open: isFormModalOpen,
    handleClose: () => onFormModalHandle(),
    body: DialogComponent,
    maxWidth: "sm" as DialogMaxWidthProps,
  };

  return (
    <>
      <Grid container>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
              marginTop: 1,
              marginBottom: 1,
            }}
          >
            <ExportDropdown />
            <Refresh onRefetch={false} />
          </Box>
          <UIDataTable {...TableProps} />
        </Grid>
      </Grid>
      <AppDialog {...DialogProps} />
    </>
  );
};

export default TemplatesPage;
