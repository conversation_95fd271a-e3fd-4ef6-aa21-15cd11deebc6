import { useCallback, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Button, Divider, IconButton, Typography } from "@mui/material";
import { Icon } from "@iconify/react";
import FormLayout from "@/ui-components/form/layout/FormLayout";
import AppButton from "@/ui-components/button/Button";
import TextInput from "@/ui-components/form/hooks/TextInput";
import { AddEditTempleteProps } from "../types";
import { TemplateSchema } from "@/lib/template";
import ExtraFieldInput from "./ExtraFieldInput";
import { useUserContext } from "@/context/userContext/UserContext";

const AddEditTemplate = ({
  closeModal,
  handleFormSubmitWrapper,
  selectedTemplate,
}: AddEditTempleteProps) => {
  const { user } = useUserContext();
  const userId = user?.id;
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<any>({
    resolver: zodResolver(TemplateSchema),
    defaultValues: {
      name: "",
      created_by: userId,
      sections: [
        {
          sectionName: "",
          contains: "",
          match_value: "",
          extract_fields: [""],
        },
      ],
    },
  }) as any;

  const { fields, append, remove } = useFieldArray({
    control,
    name: "sections",
  });

  useEffect(() => {
    if (selectedTemplate) {
      const templateName = selectedTemplate?.templateName;
      const sections = selectedTemplate.data.map((section: any) => ({
        sectionName: section.sectionName,
        contains: section.contains ? section.contains.join(", ") : "",
        match_value: section.match_value || "",
        extract_fields: Array.isArray(section.extract_fields)
          ? section.extract_fields.map((field: string) => ({ value: field }))
          : [],
      }));
      reset({ name: templateName, sections });
    }
  }, [selectedTemplate, reset]);

  return (
    <form onSubmit={handleSubmit(handleFormSubmitWrapper)}>
      <Box>
        <TextInput
          name="name"
          control={control}
          label="Template Name"
          helperText={errors?.name?.message}
        />
      </Box>
      {fields.map((field, index) => (
        <Box
          key={field.id}
          sx={{
            mt: 2,
            p: 2,
            border: "1px solid #ddd",
            borderRadius: 2,
            position: "relative",
          }}
        >
          {fields.length > 1 && (
            <IconButton
              onClick={() => remove(index)}
              sx={{
                position: "absolute",
                top: 8,
                right: 15,
                zIndex: 1,
                color: "error.main",
              }}
            >
              <Icon icon="mdi:delete-outline" width={20} height={20} />
            </IconButton>
          )}

          <Box mt={2}>
            <FormLayout>
              <TextInput
                name={`sections.${index}.sectionName`}
                control={control}
                label="Section Name"
                helperText={errors.sections?.[index]?.sectionName?.message}
              />

              <TextInput
                name={`sections.${index}.contains`}
                control={control}
                label="Contains"
                helperText={errors.sections?.[index]?.contains?.message}
              />

              <TextInput
                name={`sections.${index}.match_value`}
                control={control}
                label="Match Value"
                helperText={errors.sections?.[index]?.match_value?.message}
              />
              <Typography>Extra Fields</Typography>
              <ExtraFieldInput
                control={control}
                name={`sections.${index}.extract_fields`}
              />
            </FormLayout>
          </Box>
        </Box>
      ))}

      <Box display="flex" justifyContent="flex-end" mt={2}>
        <Button
          variant="contained"
          startIcon={<Icon icon="mdi:plus-circle-outline" />}
          onClick={() =>
            append({
              sectionName: "",
              contains: "",
              match_value: "",
              extract_fields: [""],
            })
          }
          sx={{ textTransform: "none" }}
        >
          Add Section
        </Button>
      </Box>

      <Divider sx={{ my: 2 }} />

      <Box display="flex" justifyContent="flex-end" gap={2}>
        <AppButton
          type="button"
          onClick={closeModal}
          disabled={isSubmitting}
          variant="outlined"
        >
          Cancel
        </AppButton>
        <AppButton type="submit" disabled={isSubmitting} variant="contained">
          Save
        </AppButton>
      </Box>
    </form>
  );
};

export default AddEditTemplate;
