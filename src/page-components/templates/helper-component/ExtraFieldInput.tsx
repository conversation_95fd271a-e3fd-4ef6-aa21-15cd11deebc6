import { Box, IconButton, OutlinedInput, Typography } from "@mui/material";
import { Icon } from "@iconify/react";
import { useFieldArray, Control } from "react-hook-form";
import { useEffect } from "react";

interface ExtraFieldInputProps {
  control: Control<any>;
  name: string;
}

const ExtraFieldInput = ({ control, name }: ExtraFieldInputProps) => {
  const { fields, append, remove } = useFieldArray({
    control,
    name,
  });

  useEffect(() => {
    if (fields.length === 0) {
      append({ value: "" }, { shouldFocus: false });
    }
  }, [fields.length, append]);

  return (
    <Box>
      {fields.map((field, index) => (
        <Box key={field.id} display="flex" alignItems="center" gap={1} mt={1}>
          <OutlinedInput
            placeholder={`Extra Field ${index + 1}`}
            fullWidth
            sx={{height:'60px'}}
            {...control.register(`${name}.${index}.value`)} 
          />
          {fields.length > 1 && (
            <IconButton onClick={() => remove(index)} color="error">
              <Icon icon="mdi:delete-outline" />
            </IconButton>
          )}
        </Box>
      ))}

      <Box mt={1} display="flex" justifyContent="flex-start">
        <IconButton onClick={() => append({ value: "" })} color="primary">
          <Icon icon="mdi:plus-circle-outline" />
        </IconButton>
      </Box>
    </Box>
  );
};
export default ExtraFieldInput;
