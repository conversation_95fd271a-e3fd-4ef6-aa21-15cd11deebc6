"use client";
import UIDataTable from "@/ui-components/table";
import Grid from "@mui/material/Grid";
import { GridColDef } from "@mui/x-data-grid";
import ActionMenu from "@/ui-components/table/ActionMenu";
import { useMemo } from "react";
import { UIDataTableProps } from "@/ui-components/table/types";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import AppDialog from "@/ui-components/dialog/Dialog";
import { Box, LinearProgress, Typography } from "@mui/material";
import Refresh from "@/core-components/loading/Refresh";
import { getRelativeTime } from "@/utils/DateUtils";
import { DialogMaxWidthProps } from "@/ui-components/dialog/types";
import ExportDropdown from "@/core-components/exportDropdown/ExportDropdown";
import { useDocumentsHooks } from "@/hooks/useDocuments";
import AddEditDocuments from "./helper-component/AddEditDocuments";
import { DeleteDocument } from "./helper-component/DeleteDocument";

const DocumentsPage = () => {
  const {
    state: { searchText, page, pageSize, isFormModalOpen, selectedDocuments },
    documents,
    documentLoading,
    uploadMutation,
    handleSearchInputChange,
    handlePageChange,
    handlePageSizeChange,
    onFormModalHandle,
    handleFormSubmit,
    handleFileUpload,
    handleSortModelChange,
  } = useDocumentsHooks();

  const actions = [
    {
      id: 1,
      icon: "ic:baseline-edit",
      title: "Edit",
      onItemClick: (documents: string) => {
        onFormModalHandle("edit", documents);
      },
    },
    {
      id: 3,
      icon: "ic:baseline-delete-outline",
      title: "Delete",
      onItemClick: (documents: string) => {
        onFormModalHandle("delete", documents);
      },
    },
    // {
    //   id: 3,
    //   icon: "ic:baseline-remove-red-eye",
    //   title: "View",
    //   onItemClick: (documents: string) => {
    //     onFormModalHandle("view", documents);
    //   },
    // },
  ];

  const columns: GridColDef[] = [
    {
      field: "__check__",
      headerName: "",
      width: 52,
      sortable: false,
      disableColumnMenu: true,
    },
    {
      field: "id",
      headerName: "ID",
      editable: false,
      align: "left",
      flex: 1,
      minWidth: 50,
      disableColumnMenu: true,
    },
    {
      field: "file_name",
      headerName: "FILE NAME",
      editable: false,
      align: "left",
      flex: 1,
      minWidth: 230,
      disableColumnMenu: true,
    },
    {
      field: "username",
      headerName: "USER NAME",
      headerAlign: "left",
      align: "left",
      editable: false,
      flex: 1,
      minWidth: 150,
      disableColumnMenu: true,
      renderCell: (params) => {
        const userName = params.row?.user?.name;

        return (
          <Typography variant="body2" fontWeight={600} sx={{ marginTop: 2 }}>
            {userName || "-"}
          </Typography>
        );
      },
    },
    {
      field: "file_type",
      headerName: "FILE TYPE",
      headerAlign: "left",
      editable: false,
      flex: 1,
      minWidth: 120,
      disableColumnMenu: true,
    },
    // {
    //   field: "process_type",
    //   headerName: "PROCESS TYPE",
    //   headerAlign: "left",
    //   editable: false,
    //   flex: 1,
    //   minWidth: 140,
    //   disableColumnMenu: true,
    // },

    {
      field: "description",
      headerName: "DESCRIPTION",
      headerAlign: "left",
      editable: false,
      flex: 1,
      minWidth: 140,
      disableColumnMenu: true,
    },

    {
      field: "created_at",
      headerName: "CREATED AT",
      headerAlign: "left",
      editable: false,
      flex: 1,
      minWidth: 90,
      disableColumnMenu: true,
      renderCell: (params) => {
        const relativeTime = getRelativeTime(params.value);
        return (
          <Typography variant="body2" fontWeight={600} sx={{ marginTop: 2 }}>
            {relativeTime}
          </Typography>
        );
      },
    },
    {
      field: "action",
      headerAlign: "right",
      headerName: "ACTION",
      align: "right",
      editable: false,
      sortable: false,
      flex: 1,
      minWidth: 90,
      disableColumnMenu: true,
      renderCell: (params) => (
        <ActionMenu actions={actions} params={params.row} />
      ),
    },
  ];

  const ToolbarItems = useMemo(
    () => (
      <>
        <IconifyIcon
          icon="mdi:folder-add"
          sx={{ fontSize: "20px" }}
          onClick={() => onFormModalHandle("add")}
        />
      </>
    ),
    [onFormModalHandle]
  );

  const TableDataProps = useMemo(
    () => ({
      tableData: documents?.data,
      totalPage: documents?.pagination?.totalPages,
      page: page,
      pageSize: pageSize,
      handlePageChange: handlePageChange,
      handlePageSizeChange: handlePageSizeChange,
    }),
    [page, pageSize, documents, handlePageChange, handlePageSizeChange]
  );

  const TableProps: UIDataTableProps = useMemo(
    () => ({
      tableName: "Documents",
      columns,
      searchText,
      handleSearchInputChange,
      tableDataProps: TableDataProps,
      ToolbarItems,
      handlePageChange,
      handlePageSizeChange,
      refetch: "",
    }),
    [
      columns,
      searchText,
      handleSearchInputChange,
      TableDataProps,
      ToolbarItems,
      handlePageChange,
      handlePageSizeChange,
    ]
  );

  if (documentLoading) {
    return <LinearProgress />;
  }

  const DialogMap: Record<string, React.ReactNode> = {
    add: (
      <AddEditDocuments
        closeModal={() => onFormModalHandle()}
        handleFormSubmit={handleFormSubmit}
        handleFileUpload={handleFileUpload}
        uploadMutation={uploadMutation}
      />
    ),
    edit: (
      <AddEditDocuments
        closeModal={() => onFormModalHandle()}
        handleFormSubmit={handleFormSubmit}
        handleFileUpload={handleFileUpload}
        selectedDocuments={selectedDocuments?.documents}
        uploadMutation={uploadMutation}
      />
    ),
    delete: (
      <DeleteDocument
        selectedDocument={selectedDocuments}
        onClose={() => onFormModalHandle()}
      />
    ),
  };

  const DialogTitleMap: Record<string, string> = {
    add: "Add Upload Documents",
    edit: "Edit Upload Documents",
    delete: "Delete Upload Files",
  };

  const dialogType = selectedDocuments?.type || "";
  const DialogComponent = DialogMap[dialogType] || null;
  const DialogTitleText = DialogTitleMap[dialogType] || "Add Upload Files";

  const DialogProps = {
    title: DialogTitleText,
    open: isFormModalOpen,
    handleClose: () => onFormModalHandle(),
    body: DialogComponent,
    maxWidth: "sm" as DialogMaxWidthProps,
  };

  return (
    <>
      <Grid>
        <Grid item xs={12}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
              marginTop: 1,
              marginBottom: 1,
            }}
          >
            <ExportDropdown />
            {/* <Refresh onRefetch={false} /> */}
          </Box>
          <UIDataTable {...TableProps} />
        </Grid>
      </Grid>
      <AppDialog {...DialogProps} />
    </>
  );
};

export default DocumentsPage;
