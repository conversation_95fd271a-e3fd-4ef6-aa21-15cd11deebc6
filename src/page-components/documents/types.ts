export type DocumentFormValues = {
  userId?: number;
  clientId?: number | null;
  description?: string;
  file: (File | string)[] | null;
};

export interface AddEditDocumentsProps {
  closeModal: () => void;
  userId?: string;

  selectedDocuments?: {
    id?: number;
    description?: string;
    clientId?: number;
    userId?: number;
    file_name?: string;
  };
  uploadMutation: any;
  handleFileUpload: (files: File[]) => File | null;
}
