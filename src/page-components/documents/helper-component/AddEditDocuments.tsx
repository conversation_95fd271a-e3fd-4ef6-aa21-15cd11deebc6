"use client";

import { useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import AppButton from "@/ui-components/button/Button";
import FormLayout from "@/ui-components/form/layout/FormLayout";
import TextAreaInput from "@/ui-components/form/hooks/TextAreaInput";
import SelectInput from "@/ui-components/form/hooks/SelectInput";
import FileUploaderInput from "@/ui-components/form/hooks/FileUploadInput";
import { AddEditDocumentsProps, DocumentFormValues } from "../types";
import { DocumentSchema } from "@/lib/document";
import { useUserHooks } from "@/hooks/useUser";
import { useClients } from "@/hooks/useClient";
import { useUserContext } from "@/context/userContext/UserContext";

const AddEditDocuments = ({
  closeModal,
  handleFormSubmit,
  handleFileUpload,
  selectedDocuments,
  uploadMutation,
}: AddEditDocumentsProps & {
  handleFormSubmit: (data: FormData, selectedDocuments?: any) => Promise<void>;
  handleFileUpload: (files: File[]) => File | null;
}) => {
  const { user } = useUserContext();
  const userId = user?.id;

  useEffect(() => {
    if (user?.id) {
      setValue("userId", user.id);
    }
  }, []);

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<DocumentFormValues>({
    resolver: zodResolver(DocumentSchema),
    defaultValues: {
      userId: Number(userId),
      clientId: null,
      description: "",
      file: null,
    },
  });

  useEffect(() => {
    if (selectedDocuments) {
      reset({
        userId: selectedDocuments?.userId || 0,
        clientId: selectedDocuments?.clientId || null,
        description: selectedDocuments?.description || "",
        file: null,
      });
    }
  }, [selectedDocuments, reset]);

  const handleFormSubmitWrapper = useCallback(
    async (data: DocumentFormValues) => {
      try {
        const formData = new FormData();
        if (data.file && data.file[0] instanceof File) {
          formData.append("file", data.file[0]);
        }
        formData.append("userId", String(data.userId || ""));
        formData.append("clientId", String(data.clientId || ""));
        formData.append("description", data.description || "");
        await handleFormSubmit(formData, selectedDocuments);
        closeModal();
      } catch (err) {
        console.error("Submission error:", err);
      }
    },
    [handleFormSubmit, selectedDocuments, closeModal]
  );

  const handleFileChange = useCallback(
    (files: File[]) => {
      const file = handleFileUpload(files);
      if (file) {
        setValue("file", [file], { shouldValidate: true });
      } else {
        setValue("file", null, { shouldValidate: true });
      }
    },
    [handleFileUpload, setValue]
  );

  const { userData } = useUserHooks();
  const { clientData } = useClients();

  return (
    <form onSubmit={handleSubmit(handleFormSubmitWrapper)}>
      <FormLayout>
        <FileUploaderInput
          name="file"
          control={control}
          label="Upload File *"
          onChange={handleFileChange}
          accept=".pdf,.png,.docx,.doc,.jpg,.jpeg"
          helperText={errors?.file?.message}
        />

        <SelectInput
          name="clientId"
          control={control}
          label="Client Name *"
          helperText={errors.clientId?.message}
          options={
            clientData?.data?.map((item: any) => ({
              value: item.id,
              label: item.name,
            })) || []
          }
        />

        <TextAreaInput
          name="description"
          control={control}
          label="Description"
          rows={6}
          helperText={errors.description?.message}
        />
      </FormLayout>

      <AppButton
        type="submit"
        disabled={isSubmitting || uploadMutation.isPending}
      >
        {uploadMutation.isPending ? "Uploading..." : "Save"}
      </AppButton>
      <AppButton type="button" onClick={closeModal} disabled={isSubmitting}>
        Cancel
      </AppButton>
    </form>
  );
};

export default AddEditDocuments;
