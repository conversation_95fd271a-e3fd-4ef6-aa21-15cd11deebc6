'use client';

import Analytics from '@/core-components/sections/dashboard/analytics';
import ComplexTable from '@/core-components/sections/dashboard/complex-table';
import Tasks from '@/core-components/sections/dashboard/tasks';
import TotalSpent from '@/core-components/sections/dashboard/total-spent';
import Grid from '@mui/material/Grid';


const Dashboard = () => {
  return (
    <Grid container spacing={2.5}>
      <Grid item xs={12}>
        <Analytics />
      </Grid>
      <Grid item xs={12} md={6}>
        <TotalSpent />
      </Grid>
      <Grid item xs={12} md={6} >
        <Tasks />
      </Grid>
      <Grid item xs={12}>
        <ComplexTable />
      </Grid>
    </Grid>
  );
};

export default Dashboard;
