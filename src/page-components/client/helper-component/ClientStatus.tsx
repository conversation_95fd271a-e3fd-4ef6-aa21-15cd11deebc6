import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import IconifyIcon from "@/core-components/base/IconifyIcon";

interface UserStatusProps {
  status: string;
}

const UserStatus = ({ status }: UserStatusProps) => {
  const statusText = status === "1" ? "Active" : "Inactive";
  const icon =
    status === "1" ? "ic:baseline-check-circle" : "ic:baseline-error";
  const color = status === "1" ? "success.main" : "warning.main";

  return (
    <Stack alignItems="center" spacing={0.8} sx={{ marginTop: 2 }}>
      <IconifyIcon icon={icon} color={color} fontSize="h5.fontSize" />
      <Typography variant="body2" fontWeight={600}>
        {statusText}
      </Typography>
    </Stack>
  );
};

export default UserStatus;
