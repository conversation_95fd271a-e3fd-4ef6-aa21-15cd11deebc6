import { useDeleteClients } from "@/hooks/useClient";
import FormLayout from "@/ui-components/form/layout/FormLayout";
import { Paper, Box, Typography, Button } from "@mui/material";
import { useEffect } from "react";

export const DeleteClient = ({ selectedClients, onClose }: any) => {
  const client = selectedClients;
  const id = client?.id;
  const name = client?.name;

  const { mutate, isSuccess, isPending } = useDeleteClients();

  const handleDelete = () => {
    if (id) {
      mutate(String(id));
    }
  };

  useEffect(() => {
    if (isSuccess) {
      onClose();
    }
  }, [isSuccess, onClose]);

  return (
    <form onSubmit={(e) => e.preventDefault()}>
      <Paper elevation={3} sx={{ padding: 3, borderRadius: 2 }}>
        <FormLayout>
          <Box mb={2}>
            <Typography
              variant="h6"
              component="h2"
              color="textSecondary"
              align="center"
            >
              Are you sure you want to delete this Client?
            </Typography>
          </Box>
          <Box mb={3} display="flex" justifyContent="center">
            <Typography variant="h5" component="h3" color="error">
              {name || "Unknown Name"}
            </Typography>
          </Box>
        </FormLayout>

        <Box display="flex" justifyContent="space-between" mt={3}>
          <Button
            onClick={onClose}
            color="primary"
            variant="outlined"
            fullWidth
            sx={{ marginRight: 1, color: "black" }}
            disabled={isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDelete}
            color="secondary"
            variant="contained"
            fullWidth
            sx={{
              marginLeft: 1,
              backgroundColor: "#d32f2f",
              color: "white",
              "&:hover": {
                backgroundColor: "#c62828",
              },
            }}
            disabled={isPending}
          >
            {isPending ? "Deleting..." : "Delete"}
          </Button>
        </Box>
      </Paper>
    </form>
  );
};
