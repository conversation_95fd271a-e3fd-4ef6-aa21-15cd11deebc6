"use client";
import { useCallback, useEffect } from "react";
import FormLayout from "@/ui-components/form/layout/FormLayout";
import { useForm } from "react-hook-form";
import AppButton from "@/ui-components/button/Button";
import TextAreaInput from "@/ui-components/form/hooks/TextAreaInput";
import { AddEditClientProps, ClientProps } from "../types";
import TextInput from "@/ui-components/form/hooks/TextInput";
import CheckboxInput from "@/ui-components/form/hooks/CheckboxInput";
import { ClientSchema } from "@/lib/client";
import { zodResolver } from "@hookform/resolvers/zod";
import { useUserContext } from "@/context/userContext/UserContext";

const defaultValues = {
  name: "",
  email: "",
  phone: "",
  address: "",
  status: 0,
  description: "",
};

const AddEditClient = ({
  closeModal,
  handleFormSubmit,
  selectedClients,
}: AddEditClientProps) => {
  const { user } = useUserContext();
  const userId = user?.id;

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<ClientProps>({
    resolver: zodResolver(ClientSchema),
    defaultValues,
  });

  useEffect(() => {
    if (selectedClients) {
      reset({
        name: selectedClients.name || "",
        email: selectedClients.email || "",
        phone: selectedClients.phone || "",
        address: selectedClients.address || "",
        status: selectedClients.status,
        description: selectedClients.description || "",
      });
    } else {
      reset();
    }
  }, [selectedClients, reset]);

  const handleFormSubmitWrapper = useCallback(
    (data: ClientProps) => {
      const transformedData = {
        ...data,
        id: selectedClients?.id,
        created_by: userId,
        status: data.status ? 1 : 0,
      };
      handleFormSubmit(transformedData);
      closeModal();
    },
    [handleFormSubmit, closeModal, selectedClients, userId]
  );

  return (
    <form onSubmit={handleSubmit(handleFormSubmitWrapper)}>
      <FormLayout>
        <TextInput
          name="name"
          control={control}
          label="Client Name *"
          helperText={errors.name?.message}
        />
        <TextInput
          name="email"
          control={control}
          label="Email *"
          helperText={errors.email?.message}
        />
        <TextInput
          name="phone"
          control={control}
          label="Phone *"
          helperText={errors.phone?.message}
        />
        <TextInput
          name="address"
          control={control}
          label="Address"
          helperText={errors.address?.message}
        />
        <TextAreaInput
          name="description"
          control={control}
          label="Description"
          helperText={errors.description?.message}
          rows={6}
        />
        <CheckboxInput
          name="status"
          label="Active"
          control={control}
          helperText={errors.status?.message}
        />
      </FormLayout>
      <AppButton type="submit" disabled={isSubmitting}>
        Save
      </AppButton>
      <AppButton type="button" onClick={closeModal} disabled={isSubmitting}>
        Cancel
      </AppButton>
    </form>
  );
};

export default AddEditClient;
