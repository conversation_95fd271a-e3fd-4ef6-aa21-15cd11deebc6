"use client";
import UIDataTable from "@/ui-components/table";
import Grid from "@mui/material/Grid";
import { GridColDef } from "@mui/x-data-grid";
import ActionMenu from "@/ui-components/table/ActionMenu";
import { useMemo } from "react";
import { UIDataTableProps } from "@/ui-components/table/types";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import AppDialog from "@/ui-components/dialog/Dialog";
import { DialogMaxWidthProps } from "@/ui-components/dialog/types";
import { useClients } from "@/hooks/useClient";
import UserStatus from "./helper-component/ClientStatus";
import AddEditClient from "./helper-component/Add-EditClient";
import { LinearProgress } from "@mui/material";
import { DeleteClient } from "./helper-component/DeleteClient";

const ClientComponent = () => {
  const {
    state: { searchText, page, pageSize, isFormModalOpen, selectedClients },
    clientData,
    clientDataLoading,
    handleSearchInputChange,
    handlePageChange,
    handlePageSizeChange,
    onFormModalHandle,
    handleFormSubmit,
  } = useClients();

  const actions = [
    {
      id: 1,
      icon: "ic:baseline-edit",
      title: "Edit",
      onItemClick: (clients: string) => {
        onFormModalHandle("edit", clients);
      },
    },
    {
      id: 3,
      icon: "ic:baseline-delete-outline",
      title: "Delete",
      onItemClick: (clients: string) => {
        onFormModalHandle("delete", clients);
      },
    },
  ];

  const columns: GridColDef[] = [
    {
      field: "__check__",
      headerName: "",
      width: 52,
      sortable: false,
      disableColumnMenu: true,
    },
    {
      field: "id",
      headerName: "ID",
      editable: false,
      align: "left",
      flex: 1,
      minWidth: 50,
      disableColumnMenu: true,
    },
    {
      field: "name",
      headerName: "CLIENT NAME",
      editable: false,
      align: "left",
      flex: 1,
      minWidth: 230,
      disableColumnMenu: true,
    },
    {
      field: "email",
      headerName: "Email",
      headerAlign: "left",
      align: "left",
      editable: false,
      flex: 1,
      minWidth: 150,
      disableColumnMenu: true,
    },
    {
      field: "phone",
      headerName: "PHONE",
      headerAlign: "left",
      editable: false,
      flex: 1,
      minWidth: 120,
      disableColumnMenu: true,
    },
    {
      field: "status",
      headerName: "STATUS",
      headerAlign: "left",
      align: "left",
      editable: false,
      flex: 1,
      minWidth: 130,
      disableColumnMenu: true,
      renderCell: (params) => {
        const statusRes = params.value != null ? params.value.toString() : "";

        return <UserStatus status={statusRes} />;
      },
    },
    {
      field: "action",
      headerAlign: "right",
      headerName: "ACTION",
      align: "right",
      editable: false,
      sortable: false,
      flex: 1,
      minWidth: 90,
      disableColumnMenu: true,
      renderCell: (params) => {
        console.log("params", params);

        return <ActionMenu actions={actions} params={params.row} />;
      },
    },
  ];

  const ToolbarItems = useMemo(
    () => (
      <>
        <IconifyIcon
          icon="mdi:folder-add"
          sx={{ fontSize: "20px" }}
          onClick={() => onFormModalHandle("add")}
        />
      </>
    ),
    [onFormModalHandle]
  );

  const TableDataProps = useMemo(
    () => ({
      tableData: clientData?.data,
      totalPage: clientData?.pagination?.totalPages,
      page,
      pageSize,
      handlePageChange,
      handlePageSizeChange,
    }),
    [clientData, page, pageSize]
  );

  const TableProps: UIDataTableProps = useMemo(
    () => ({
      tableName: "Client",
      columns,
      searchText,
      handleSearchInputChange,
      tableDataProps: TableDataProps,
      ToolbarItems,
      handlePageChange,
      handlePageSizeChange,
      refetch: "",
    }),
    [
      columns,
      searchText,
      handleSearchInputChange,
      TableDataProps,
      ToolbarItems,
      handlePageChange,
      handlePageSizeChange,
    ]
  );

  if (clientDataLoading) {
    return <LinearProgress />;
  }
  const DialogMap: Record<string, React.ReactNode> = {
    add: (
      <AddEditClient
        closeModal={() => onFormModalHandle()}
        handleFormSubmit={handleFormSubmit}
      />
    ),
    edit: (
      <AddEditClient
        closeModal={() => onFormModalHandle()}
        handleFormSubmit={handleFormSubmit}
        selectedClients={selectedClients}
      />
    ),
    delete: (
      <DeleteClient
        selectedClients={selectedClients}
        onClose={() => onFormModalHandle()}
      />
    ),
  };
  console.log("sk", selectedClients?.clients);

  const DialogTitleMap: Record<string, string> = {
    add: "Add Client",
    edit: "Edit Client",
    delete: "Delete Client",
  };

  const dialogType = selectedClients?.type || "";
  const DialogComponent = DialogMap[dialogType] || null;
  const DialogTitleText = DialogTitleMap[dialogType] || "Client Data";

  const DialogProps = {
    title: DialogTitleText,
    open: isFormModalOpen,
    handleClose: () => onFormModalHandle(),
    body: DialogComponent,
    maxWidth: "md" as DialogMaxWidthProps,
  };

  return (
    <>
      <Grid>
        <UIDataTable {...TableProps} />
      </Grid>
      <AppDialog {...DialogProps} />
    </>
  );
};

export default ClientComponent;
