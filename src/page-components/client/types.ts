export type ClientProps = {
  id?: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  status: number | boolean;
  description: string;
};

export type AddEditClientProps = {
  selectedClients?: ClientProps | null;
  closeModal: () => void;
  handleFormSubmit: (data: ClientProps) => void;
};

export type RowsProps = {
  id: number;
  name: string;
  description: string;
  updated_at: string;
};
