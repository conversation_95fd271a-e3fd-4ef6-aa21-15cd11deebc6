"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { User, UserContextType } from "./type";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";
const UserContext = createContext<UserContextType | undefined>(undefined);

const initialUser = (() => {
  try {
    const stored = localStorage.getItem("user");
    return stored ? JSON.parse(stored) : null;
  } catch {
    return null;
  }
})();
export const UserProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(initialUser);
  const router = useRouter();
  const queryClient = useQueryClient();
  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    const token = Cookies.get("token");

    if (storedUser && token) {
      try {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);
      } catch (error) {
        console.error("Failed to parse stored user:", error);
        setUser(null);
      }
    } else {
      console.warn("User or token not found");
    }
  }, []);

  const logout = () => {
    Cookies.remove("token");
    localStorage.removeItem("user");
    setUser(null);
    queryClient.invalidateQueries({ queryKey: ["cart"] });
    router.push("/");
  };

  return (
    <UserContext.Provider value={{ user, setUser, logout }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUserContext = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
