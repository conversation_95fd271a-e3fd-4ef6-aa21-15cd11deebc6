import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(request: NextRequest) {
  const token = request.cookies.get("token")?.value;
  const tokenExpiration = request.cookies.get("tokenExpiration")?.value;

  if (!token || !tokenExpiration) {
    const response = NextResponse.redirect(
      new URL("/authentication/sign-in/", request.url)
    );
    response.headers.set("Cache-Control", "no-store");
    return response;
  }

  const expirationTime = new Date(tokenExpiration);

  if (expirationTime <= new Date()) {
    const response = NextResponse.redirect(
      new URL("/authentication/sign-in/", request.url)
    );
    response.headers.set("Cache-Control", "no-store");
    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/',
    '/inventory/:path*',
    '/customers/:path*',
    '/orders/:path*',
    '/banners/:path*',
    '/dealsAndPromotions/:path*',
    '/notifications/:path*',
    '/faqaManagement/:path*',
    '/deliveryBoys/:path*',
    '/globalLocality/:path*',
    '/reports/:path*',
    '/localities/:path*',
    '/ordermanagement/:path*'
  ],
};
