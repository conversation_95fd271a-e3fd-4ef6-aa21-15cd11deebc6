import { useCallback, useReducer } from "react";
import documentService from "@/services/documentService/documents.Service";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { DocumentFormValues } from "@/page-components/documents/types";

// -------------------------
// Types
// -------------------------
type UploadedImage = {
  file_name: string;
  mime_type: string;
  size: number;
  model_type: string;
} | null;

type SelectedDocuments = {
  type: "add" | "edit" | "delete";
  documents: any;
} | null;

type InitialStateProps = {
  searchText: string;
  page: number;
  pageSize: number;
  isFormModalOpen: boolean;
  selectedDocuments: SelectedDocuments;
  uploadedImage: UploadedImage;
  sortField: string | null;
  sortOrder: "asc" | "desc" | null;
};

type StateAction =
  | { type: "SEARCH_TEXT"; payload: string }
  | { type: "PAGE"; payload: number }
  | { type: "PAGE_SIZE"; payload: number }
  | { type: "FORM_MODAL_OPEN"; payload: boolean }
  | { type: "SELECTED_DOCUMENTS"; payload: SelectedDocuments }
  | { type: "UPLOADED_IMAGE"; payload: UploadedImage }
  | { type: "SORT_FIELD"; payload: string }
  | { type: "SORT_ORDER"; payload: "asc" | "desc" }
  | { type: "CLEAR" };

// -------------------------
// Initial State & Reducer
// -------------------------
const initialState: InitialStateProps = {
  searchText: "",
  page: 1,
  pageSize: 10,
  isFormModalOpen: false,
  selectedDocuments: null,
  uploadedImage: null,
  sortField: null,
  sortOrder: null,
};

const reducer = (
  state: InitialStateProps,
  action: StateAction
): InitialStateProps => {
  switch (action.type) {
    case "SEARCH_TEXT":
      return { ...state, searchText: action.payload };
    case "PAGE":
      return { ...state, page: action.payload };
    case "PAGE_SIZE":
      return { ...state, pageSize: action.payload, page: 1 };
    case "FORM_MODAL_OPEN":
      return { ...state, isFormModalOpen: action.payload };
    case "SELECTED_DOCUMENTS":
      return { ...state, selectedDocuments: action.payload };
    case "UPLOADED_IMAGE":
      return { ...state, uploadedImage: action.payload };
    case "SORT_FIELD":
      return { ...state, sortField: action.payload };
    case "SORT_ORDER":
      return { ...state, sortOrder: action.payload };
    case "CLEAR":
      return initialState;
    default:
      return state;
  }
};

// -------------------------
// Main Hook
// -------------------------
export const useDocumentsHooks = () => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const documentsQueryParams = {
    page: state.page,
    limit: state.pageSize,
    searchTerm: state.searchText,
  };

  const { data: documents, isLoading: documentLoading } = useDocuments(
    documentsQueryParams
  ) as any;

  const uploadMutation = useUploadDocument();
  const updateMutation = useUpdateDocument();

  const handleSearchInputChange = useCallback((query: string) => {
    dispatch({ type: "SEARCH_TEXT", payload: query });
  }, []);

  const handlePageChange = useCallback((newPage: number) => {
    dispatch({ type: "PAGE", payload: newPage });
  }, []);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    dispatch({ type: "PAGE_SIZE", payload: newPageSize });
  }, []);

  const onFormModalHandle = useCallback(
    (type?: "add" | "edit" | "delete", documents?: any) => {
      dispatch({
        type: "SELECTED_DOCUMENTS",
        payload: type ? { type, documents: documents || null } : null,
      });
      dispatch({ type: "FORM_MODAL_OPEN", payload: !state.isFormModalOpen });
    },
    [state.isFormModalOpen]
  );

  const handleFormSubmit = useCallback(
    async (formData: FormData, selectedDocuments: any) => {
      console.log("selectedDocuments", selectedDocuments);

      try {
        if (selectedDocuments?.id) {
          await updateMutation.mutateAsync({
            id: selectedDocuments.id,
            data: formData,
          });
        } else {
          await uploadMutation.mutateAsync(formData);
        }
      } catch (error) {
        console.error("Form submission error:", error);
        throw error;
      }
    },
    [updateMutation, uploadMutation]
  );

  const handleFormSubmitWrapper = useCallback(
    async (data: DocumentFormValues) => {
      const formData = new FormData();
      if (data.file && data.file[0]) formData.append("file", data.file[0]);
      if (data.description) formData.append("description", data.description);

      await handleFormSubmit(formData, state.selectedDocuments);
      dispatch({ type: "FORM_MODAL_OPEN", payload: false });
    },
    [handleFormSubmit, state.selectedDocuments]
  );

  const handleSortModelChange = useCallback((sortModel: any[]) => {
    if (sortModel.length > 0) {
      dispatch({ type: "SORT_FIELD", payload: sortModel[0]?.field });
      dispatch({ type: "SORT_ORDER", payload: sortModel[0]?.sort });
    }
  }, []);

  const handleFileUpload = useCallback((files: File[]) => {
    if (files.length > 0) {
      const file = files[0];
      const payload = {
        file_name: file.name,
        mime_type: file.type,
        size: file.size,
        model_type: "document",
      };
      dispatch({ type: "UPLOADED_IMAGE", payload });
      return file;
    }
    dispatch({ type: "UPLOADED_IMAGE", payload: null });
    return null;
  }, []);

  return {
    state,
    documents,
    documentLoading,
    uploadMutation,
    handleFileUpload,
    handleSearchInputChange,
    handlePageChange,
    handlePageSizeChange,
    onFormModalHandle,
    handleFormSubmit,
    handleFormSubmitWrapper,
    handleSortModelChange,
  };
};

// -------------------------
// Helper Hooks
// -------------------------
export const useDocuments = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: ["documents", params],
    queryFn: () =>
      documentService.getDocuments(
        params.page,
        params.limit,
        params.searchTerm
      ),
  });
};

export const useDocument = (id: string | number) => {
  return useQuery({
    queryKey: ["document", id],
    queryFn: () => documentService.getDocumentById(id),
    enabled: !!id,
  });
};

export const useUploadDocument = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: FormData) => documentService.uploadDocument(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};

export const useUpdateDocument = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string | number; data: FormData }) =>
      documentService.updateDocument(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};

export const useDeleteDocument = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string | number) => documentService.deleteDocument(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["documents"] });
    },
  });
};
