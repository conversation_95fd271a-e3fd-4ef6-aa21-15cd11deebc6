import { queryClient } from "@/lib/queryClient";
import { TemplateFormType } from "@/lib/template";
import templateService from "@/services/templateService/template.Service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useReducer } from "react";

type InitialStateProps = {
  searchText: string;
  page: number;
  pageSize: number;
  isFormModalOpen: boolean;
  selectedTemplate: any;
  uploadedImage: {
    file_name: string;
    mime_type: string;
    size: number;
    model_type: string;
  } | null;
  sortField: string | null;
  sortOrder: "asc" | "desc" | null;
};

interface StateAction {
  type:
    | "SEARCH_TEXT"
    | "PAGE"
    | "PAGE_SIZE"
    | "FORM_MODAL_OPEN"
    | "SELECTED_TEMPLATE"
    | "UPLOADED_IMAGE"
    | "SORT_FIELD"
    | "SORT_ORDER"
    | "CLEAR";
  payload?: any;
}

const initialState: InitialStateProps = {
  searchText: "",
  page: 1,
  pageSize: 10,
  isFormModalOpen: false,
  selectedTemplate: null,
  uploadedImage: null,
  sortField: null,
  sortOrder: null,
};

const reducer = (state: InitialStateProps, action: StateAction) => {
  switch (action.type) {
    case "SEARCH_TEXT":
      return { ...state, searchText: action.payload };
    case "PAGE":
      return { ...state, page: action.payload };
    case "PAGE_SIZE":
      return { ...state, pageSize: action.payload };
    case "FORM_MODAL_OPEN":
      return { ...state, isFormModalOpen: action.payload };
    case "SELECTED_TEMPLATE":
      return { ...state, selectedTemplate: action.payload };
    case "UPLOADED_IMAGE":
      return { ...state, uploadedImage: action.payload };
    case "SORT_FIELD":
      return { ...state, sortField: action.payload };
    case "SORT_ORDER":
      return { ...state, sortOrder: action.payload };
    default:
      return state;
  }
};

export const useTemplete = () => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const temepleteParams = {
    page: state.page,
    limit: state.pageSize,
    searchTerm: state.searchText,
  };

  const { data: templateData, isLoading: templateLoading } = useGetTemplate(
    temepleteParams
  ) as any;

  const handleSearchInputChange = useCallback((query: string) => {
    dispatch({ type: "SEARCH_TEXT", payload: query });
  }, []);

  const handlePageChange = useCallback((page: number) => {
    dispatch({ type: "PAGE", payload: page });
  }, []);

  const handlePageSizeChange = useCallback((page: number) => {
    dispatch({ type: "PAGE_SIZE", payload: page });
    dispatch({ type: "PAGE", payload: 1 });
  }, []);

  const onFormModalHandle = useCallback(
    (type?: string, templete?: any) => {
      if (
        type === "add" ||
        type === "edit" ||
        type === "delete" ||
        type === "view"
      ) {
        dispatch({
          type: "SELECTED_TEMPLATE",
          payload: { type, templete: templete || null },
        });
      } else {
        dispatch({ type: "SELECTED_TEMPLATE", payload: null });
      }
      dispatch({ type: "FORM_MODAL_OPEN", payload: !state.isFormModalOpen });
    },
    [state.isFormModalOpen]
  );

  const handleImageUpload = useCallback(async (files: File[]) => {
    const payload = {
      file_name: files[0].name,
      mime_type: files[0].type,
      size: files[0].size,
      model_type: "App\\Models\\Category",
    };
    dispatch({ type: "UPLOADED_IMAGE", payload });
  }, []);

  const { mutate: handleFormSubmit } = useMutation({
    mutationFn: (data: any) => {
      if (state.selectedTemplate?.type === "edit") {
        return templateService.updateTemplate(
          state.selectedTemplate.templete.id,
          data
        );
      }
      return templateService.createTemplate(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["templates"] });
      dispatch({ type: "FORM_MODAL_OPEN", payload: false });
    },
    onError: (error) => {
      console.error("Error submitting form:", error);
    },
  });

  const handleFormSubmitWrapper = useCallback(
    (formData: TemplateFormType) => {
      const sectionsArray = formData.sections.map((section) => {
        const sectionName = section.sectionName?.trim();
        const extract_fields = section.extract_fields
          .map((field) => ({ value: field.value.trim() }))
          .filter((f) => f.value !== "");

        const payload: any = {
          sectionName,
          extract_fields,
        };

        const contains = section.contains?.trim();
        if (contains) payload.contains = contains;

        const matchValue = section.match_value?.trim();
        if (matchValue) payload.match_value = matchValue;

        return payload;
      });

      const payload = {
        name: formData.name,
        created_by: formData.created_by,
        sections: sectionsArray,
      };

      handleFormSubmit(payload);
    },
    [handleFormSubmit]
  );

  const handleSortModelChange = useCallback((sortModel: any) => {
    if (sortModel.length > 0) {
      const sortField = sortModel[0]?.field;
      const sortOrder = sortModel[0]?.sort;
      dispatch({ type: "SORT_FIELD", payload: sortField });
      dispatch({ type: "SORT_ORDER", payload: sortOrder });
    }
  }, []);

  return {
    state,
    templateData,
    templateLoading,
    handleSearchInputChange,
    handlePageChange,
    handlePageSizeChange,
    onFormModalHandle,
    handleFormSubmit,
    handleFormSubmitWrapper,
    handleImageUpload,
    handleSortModelChange,
  };
};

export const useGetTemplate = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: ["templates", params],
    queryFn: () =>
      templateService.getTemplates(
        params.page,
        params.limit,
        params.searchTerm
      ),
    placeholderData: (previousData) => previousData,
  });
};

export const useTemplateId = (id: string | number) => {
  return useQuery({
    queryKey: ["templates", id],
    queryFn: () => templateService.getTemplateById(id),
    enabled: !!id,
  });
};

export const useUpdateTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string | number; data: any }) =>
      templateService.updateTemplate(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["templates"] });
    },
  });
};

export const useDeleteTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string | number) => templateService.deleteTemplate(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["templates"] });
    },
  });
};
