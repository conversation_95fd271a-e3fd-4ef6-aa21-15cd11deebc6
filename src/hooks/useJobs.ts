import { useReducer, useCallback, useState } from "react";
import { JobsProps } from "@/page-components/jobs/types";
import jobService from "@/services/jobService/job.service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import clientService from "@/services/clientService/client.Services";
import usersService from "@/services/userService/user.service";

type InitialState = {
  searchText: string;
  page: number;
  pageSize: number;
  isFormModalOpen: boolean;
  selectedJobs: any;
  sortField: string | null;
  sortOrder: "asc" | "desc" | null;
  selectedClient: any;
  selectedUser: any;
  selectedJobType: any;
  jobs: any[];
};

type Action =
  | { type: "SEARCH_TEXT"; payload: string }
  | { type: "PAGE"; payload: number }
  | { type: "PAGE_SIZE"; payload: number }
  | { type: "FORM_MODAL_OPEN"; payload: boolean }
  | { type: "SELECTED_CLIENTS"; payload: any }
  | { type: "ADD_CLIENT"; payload: JobsProps }
  | { type: "UPDATE_CLIENT"; payload: JobsProps }
  | { type: "SORT_FIELD"; payload: string }
  | { type: "SELECTED_CLIENT"; payload: any }
  | { type: "SELECTED_JOB_TYPE"; payload: any }
  | { type: "SELECTED_USER"; payload: any }
  | { type: "SORT_ORDER"; payload: "asc" | "desc" };

const initialState: InitialState = {
  searchText: "",
  page: 1,
  pageSize: 10,
  isFormModalOpen: false,
  selectedJobs: null,
  sortField: null,
  sortOrder: null,
  selectedClient: { value: 1, label: "Default Client Name" },
  selectedJobType: { value: "SEW", label: "Default Job Name" },
  selectedUser: { value: 1, label: "Default User Name" },
  jobs: [],
};

const reducer = (state: InitialState, action: Action): InitialState => {
  switch (action.type) {
    case "SEARCH_TEXT":
      return { ...state, searchText: action.payload };
    case "PAGE":
      return { ...state, page: action.payload };
    case "PAGE_SIZE":
      return { ...state, pageSize: action.payload, page: 1 };
    case "FORM_MODAL_OPEN":
      return { ...state, isFormModalOpen: action.payload };
    case "SELECTED_CLIENTS":
      return { ...state, selectedJobs: action.payload };
    case "ADD_CLIENT":
      return { ...state, jobs: [...state.jobs, action.payload] };
    case "SELECTED_CLIENT":
      return { ...state, selectedClient: action.payload };
    case "SELECTED_USER":
      return { ...state, selectedUser: action.payload };
    case "SELECTED_JOB_TYPE":
      return { ...state, selectedJobType: action.payload };

    case "UPDATE_CLIENT":
      return {
        ...state,
        jobs: state.jobs.map((job) =>
          job.id === action.payload.id ? action.payload : job
        ),
      };
    case "SORT_FIELD":
      return { ...state, sortField: action.payload };
    case "SORT_ORDER":
      return { ...state, sortOrder: action.payload };
    default:
      return state;
  }
};

export const useJobs = () => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const jobQueryParams = {
    page: state.page,
    limit: state.pageSize,
    searchTerm: state.searchText,
    clientId: state.selectedClient?.value,
    jobTypeId: state.selectedJobType?.value,
    userId: state.selectedUser?.value,
  };

  const { data: clientData, isLoading: clientDataLoading } =
    useGetClients() as any;

  const { data: jobDatas, isLoading: jobDataLoading } = useGetJobs(
    jobQueryParams
  ) as any;

  const { data: userData } = useGetUsers();

  const createJobMutation = useCreateJobs();
  const updateJobMutation = useUpdateJobs();

  const handleFormSubmit = async (jobs: any) => {
    try {
      const isEditMode = !!state.selectedJobs?.jobs?.id;

      if (isEditMode) {
        const id = state.selectedJobs.jobs.id;
        await updateJobMutation.mutateAsync({ id, data: jobs });
      } else {
        await createJobMutation.mutateAsync(jobs);
      }

      dispatch({ type: "FORM_MODAL_OPEN", payload: false });
    } catch (error) {
      console.error("Error saving job:", error);
    }
  };

  const handleRunClick = async (data: any) => {
    try {
      const jobId = data?.id;
      const jobType=data?.job_type;
      const fileName = data?.documents?.map(
        (fileName: any) => fileName.file_name
      );
      // const fileResponse = await fetch(`/${fileName}`);
      // const blob = await fileResponse.blob();

      await jobService.updateJobs(jobId, { status: "In Progress" });

      const formData = new FormData();
      // formData.append("file", blob, fileName);
      // formData.append("template_id", data?.templateId);
      formData.append("job_id", data?.id);

      const uploadResponse = await fetch(
        "http://13.201.217.157:8000/run-agent",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            job_id: jobId,
            job_type: jobType,
            file_names: fileName,
          }),
        }
      );

      if (!uploadResponse.ok) {
        const error = await uploadResponse.json();
        console.error("Agent Error:", error);
        throw new Error(error.error || "Agent task failed.");
      }

      const responseData = await uploadResponse.json();
      console.log("✅ Agent Success:", responseData);

      // await fetch("/api/save-json", {
      //   method: "POST",
      //   headers: { "Content-Type": "application/json" },
      //   body: JSON.stringify(responseData),
      // });
      await jobService.updateJobs(jobId, { status: "Completed" });
    } catch (error) {
      if (data?.id) {
        await jobService.updateJobs(data.id, { status: "Failed" });
      }
      console.log("error", error);
    }
  };

  const onFormModalHandle = useCallback(
    (type?: string, jobs?: any) => {
      if (["add", "edit", "delete", "view"].includes(type || "")) {
        dispatch({ type: "SELECTED_CLIENTS", payload: { type, jobs } });
      } else {
        dispatch({ type: "SELECTED_CLIENTS", payload: null });
      }
      dispatch({ type: "FORM_MODAL_OPEN", payload: !state.isFormModalOpen });
    },
    [state.isFormModalOpen]
  );

  const handlePageChange = useCallback((newPage: number) => {
    dispatch({ type: "PAGE", payload: newPage });
  }, []);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    dispatch({ type: "PAGE_SIZE", payload: newPageSize });
    dispatch({ type: "PAGE", payload: 1 });
  }, []);

  const handleSearchInputChange = useCallback((query: string) => {
    dispatch({ type: "SEARCH_TEXT", payload: query });
  }, []);

  const handleClientChange = useCallback((client: any) => {
    dispatch({ type: "SELECTED_CLIENT", payload: client });
    dispatch({ type: "PAGE", payload: 1 });
  }, []);

  const handleJobTypeChange = useCallback((jobType: any) => {
    dispatch({ type: "SELECTED_JOB_TYPE", payload: jobType });
    dispatch({ type: "PAGE", payload: 1 });
  }, []);

  const handleUserChange = useCallback((user: any) => {
    dispatch({ type: "SELECTED_USER", payload: user });
    dispatch({ type: "PAGE", payload: 1 });
  }, []);

  return {
    state,
    jobDatas,
    jobDataLoading,
    clientData,
    userData,
    handleFormSubmit,
    handleRunClick,
    onFormModalHandle,
    handlePageChange,
    handlePageSizeChange,
    handleSearchInputChange,
    handleClientChange,
    handleJobTypeChange,
    handleUserChange,
  };
};

export const useCreateJobs = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: JobsProps) => jobService.createJobs(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["jobs"] });
    },
    onError: (error) => {
      console.error("Failed to create job:", error);
    },
  });
};

export const useGetJobs = (params: Record<string, any> = {}) =>
  useQuery({
    queryKey: ["jobs", params],
    refetchInterval: 5000,
    queryFn: () =>
      jobService.getJobs(
        params.page,
        params.limit,
        params.searchTerm,
        params.clientId,
        params.jobTypeId,
        params.userId
      ),
  });

export const useGetByIdJobs = (id: string | number) =>
  useQuery({
    queryKey: ["jobs", id],
    queryFn: () => jobService.getJobsById(id),
    enabled: !!id,
  });

export const useUpdateJobs = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string | number;
      data: Partial<JobsProps>;
    }) => jobService.updateJobs(id, data),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["jobs"] }),
  });
};

export const useDeleteJobs = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string | number) => jobService.deleteJobs(id),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["jobs"] }),
  });
};

export const useGetClients = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: ["clients", params],
    queryFn: () =>
      clientService.getClients(params.page, params.limit, params.searchTerm),
  });
};

export const useGetUsers = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: ["users", params],
    queryFn: () =>
      usersService.getUsers(params.page, params.limit, params.searchTerm),
    placeholderData: (previousData) => previousData,
  });
};
