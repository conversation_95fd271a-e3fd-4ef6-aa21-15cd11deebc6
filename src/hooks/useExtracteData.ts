import { useReducer, useCallback, useState, useEffect } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { saveAs } from "file-saver";
import extracteDataService from "@/services/extracteData/extracteData.service";
import { ClientProps } from "@/page-components/client/types";
import { sewColumns } from "@/page-components/extracteData/extractedDataDetails/helper-component/sewColumn";
import clientService from "@/services/clientService/client.Services";
import dayjs from "dayjs";
import jobService from "@/services/jobService/job.service";

type InitialStateProps = {
  searchText: string;
  page: number;
  pageSize: number;
  isFormModalOpen: boolean;
  selectedExtractedData: any;
  sortField: string | null;
  sortOrder: "asc" | "desc" | null;
  selectedClient: any;
  selectedJobType: any;
  startDate: any;
  clients: any[];
};

interface StateAction {
  type:
    | "SEARCH_TEXT"
    | "PAGE"
    | "PAGE_SIZE"
    | "FORM_MODAL_OPEN"
    | "SELECTED_EXTRACTED_DATA"
    | "ADD_CLIENT"
    | "UPDATE_CLIENT"
    | "SORT_FIELD"
    | "SELECTED_CLIENT"
    | "START_DATE"
    | "SELECTED_JOB_TYPE"
    | "SORT_ORDER";
  payload?: any;
}
const startDate = dayjs().format("YYYY-MM-DD");

const initialState: InitialStateProps = {
  searchText: "",
  page: 1,
  pageSize: 10,
  isFormModalOpen: false,
  selectedExtractedData: null,
  sortField: null,
  sortOrder: null,
  selectedClient: { value: 1, label: "Default Client Name" },
  selectedJobType: { value: "SEW", label: "Default Job Name" },
  startDate: startDate,
  clients: [],
};

const reducer = (state: InitialStateProps, action: StateAction) => {
  switch (action.type) {
    case "SEARCH_TEXT":
      return { ...state, searchText: action.payload };
    case "PAGE":
      return { ...state, page: action.payload };
    case "PAGE_SIZE":
      return { ...state, pageSize: action.payload, page: 1 };
    case "FORM_MODAL_OPEN":
      return { ...state, isFormModalOpen: action.payload };
    case "SELECTED_EXTRACTED_DATA":
      return { ...state, selectedExtractedData: action.payload };
    case "SORT_FIELD":
      return { ...state, sortField: action.payload };
    case "SORT_ORDER":
      return { ...state, sortOrder: action.payload };
    case "ADD_CLIENT":
      return { ...state, clients: [...state.clients, action.payload] };
    case "SELECTED_CLIENT":
      return { ...state, selectedClient: action.payload };
    case "SELECTED_JOB_TYPE":
      return { ...state, selectedJobType: action.payload };

    case "START_DATE":
      return { ...state, startDate: action.payload };
    case "UPDATE_CLIENT":
      return {
        ...state,
        clients: state.clients.map((client) =>
          client.id === action.payload.id ? action.payload : client
        ),
      };
    default:
      return state;
  }
};

export const useExtracteDataHooks = (id?: string | number) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const { selectedExtractedData } = state;

  const [rows, setRows] = useState<any[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [editingRow, setEditingRow] = useState<number | null>(null);
  const [editedRowData, setEditedRowData] = useState<any>(null);
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const [totalFiles, setTotalFiles] = useState(0);

  const updateMutation = useUpdateExtractedData() as any;

  const { data: clientData, isLoading: clientDataLoading } =
    useGetClients() as any;

  const { data: jobDatas, isLoading: jobDataLoading } = useGetJobs();

  const handleRowClick = (rowIndex: number) => {
    if (editingRow !== null) return;
    setEditingRow(rowIndex);
    setEditedRowData({ ...rows[rowIndex] });
  };

  const handleCellChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    columnId: string
  ) => {
    setEditedRowData({
      ...editedRowData,
      [columnId]: e.target.value,
    });
  };

  const handleSave = async () => {
    if (editingRow !== null && editedRowData) {
      try {
        const updatedRows = [...rows];
        updatedRows[editingRow] = editedRowData;
        setRows(updatedRows);

        setEditingRow(null);
        setEditedRowData(null);

        if (extractedIdData?.id) {
          await updateMutation.mutateAsync({
            id: extractedIdData?.id,
            data: editedRowData,
          });
        }
      } catch (error) {
        console.error("Failed to save data:", error);
      }
    }
  };

  const handleCancel = () => {
    setEditingRow(null);
    setEditedRowData(null);
  };

  const extracteDataQueryParams = {
    page: state.page,
    limit: state.pageSize,
    searchTerm: state.searchText,
    clientId: state.selectedClient?.value,
    jobTypeId: state.selectedJobType?.value,
    startDate: state.startDate,
  };

  const { data: extractedData, isLoading: extracteDataLoading } =
    useGetExtracteData(extracteDataQueryParams) as any;
  const { data: extractedIdData, isLoading: extractedPopupLoading } =
    useGetExtracteIdData(id) as any;

  const handleFormSubmit = async (clientData: ClientProps) => {
    // Implement form submission logic if needed
  };

  const handleSearchInputChange = useCallback((query: string) => {
    dispatch({ type: "SEARCH_TEXT", payload: query });
  }, []);

  const handlePageChange = useCallback((newPage: number) => {
    dispatch({ type: "PAGE", payload: newPage });
  }, []);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    dispatch({ type: "PAGE_SIZE", payload: newPageSize });
    dispatch({ type: "PAGE", payload: 1 });
  }, []);

  const handleClientChange = useCallback((client: any) => {
    dispatch({ type: "SELECTED_CLIENT", payload: client });
    dispatch({ type: "PAGE", payload: 1 });
  }, []);

  const handleJobTypeChange = useCallback((jobType: any) => {
    dispatch({ type: "SELECTED_JOB_TYPE", payload: jobType });
    dispatch({ type: "PAGE", payload: 1 });
  }, []);

  const handleDateChange = useCallback((date: any) => {
    dispatch({ type: "START_DATE", payload: date });
    dispatch({ type: "PAGE", payload: 1 });
  }, []);

  const onFormModalHandle = useCallback(
    (type?: string, clients?: any) => {
      if (type === "add" || type === "edit" || type === "view") {
        dispatch({
          type: "SELECTED_EXTRACTED_DATA",
          payload: { type, ...clients },
        });
      } else {
        dispatch({ type: "SELECTED_EXTRACTED_DATA", payload: null });
      }
      dispatch({ type: "FORM_MODAL_OPEN", payload: !state.isFormModalOpen });
    },
    [state.isFormModalOpen]
  );

  const getCountry = (state: string): string => {
    const australianStates = [
      "NSW",
      "VIC",
      "QLD",
      "SA",
      "WA",
      "TAS",
      "NT",
      "ACT",
    ];
    return australianStates.includes(state?.toUpperCase())
      ? "AUSTRALIA"
      : "INTL";
  };

  const formatDate = (dateStr: string) => {
    if (!dateStr) return "";
    const cleanStr = dateStr.replace(/\D/g, "");
    if (cleanStr.length >= 8) {
      const day = cleanStr.substring(0, 2);
      const month = cleanStr.substring(2, 4);
      const year = cleanStr.substring(4, 8);
      return `${year}-${month}-${day}`;
    }
    return dateStr;
  };
const transformDataToRows = (fileData: any) => {
  if (!fileData?.data) return [];

  const rows: any[] = [];
  const zipData = fileData.data;
  if (!zipData) return [];

  // Iterate over each PDF file in zipData
  Object.keys(zipData).forEach((pdfKey) => {
    if (!pdfKey.endsWith('.pdf')) return; // Skip non-PDF keys
    const pdfData = zipData[pdfKey];
    const imageData = pdfKey; // Use PDF file name as imageData (e.g., sew_1_1_20250611203005.pdf)

    const propertyAddress = pdfData.title_and_transaction?.property_address || {};

    const toUpperCase = (value: string | undefined) =>
      value ? value.toUpperCase() : undefined;

    const createRow = (vendorData: any, purchaserData: any) => {
      const row: any = {
        ...(imageData && { Image: imageData }), // Set Image to PDF file name
        ...(pdfData.document_type && {
          DocumentType: pdfData.document_type,
        }),
        EmailHardCopy: "Email",
        ...(pdfData.document_type && {
          Template: pdfData.document_type === "NOD" ? "NODT1" : "NOAT1",
        }),
        Handwritten: pdfData.handwritten || "No",
        ...((propertyAddress.flat_unit_number || propertyAddress.flatunit) && {
          FlatUnitNumber:
            propertyAddress.flat_unit_number || propertyAddress.flatunit,
        }),
        ...(propertyAddress.street_number && {
          StreetNumber: propertyAddress.street_number,
        }),
        ...(propertyAddress.street_name && {
          StreetName: toUpperCase(propertyAddress.street_name),
        }),
        ...(propertyAddress.street_type && {
          StreetType: toUpperCase(propertyAddress.street_type),
        }),
        ...(propertyAddress.suburb && {
          Suburb: toUpperCase(propertyAddress.suburb),
        }),
        ...(propertyAddress.postcode && { Postcode: propertyAddress.postcode }),
        ...(pdfData.title_and_transaction?.lot_number && {
          LotNumber: pdfData.title_and_transaction.lot_number,
        }),
        ...(pdfData.title_and_transaction?.plan_number && {
          PlanNumber: pdfData.title_and_transaction.plan_number,
        }),
        ...(pdfData.title_and_transaction?.date_of_transfer && {
          DateOfTransfer: formatDate(
            pdfData.title_and_transaction.date_of_transfer
          ),
        }),
      };

      if (vendorData) {
        const vendorAddress = vendorData.address || {};
        const vendorFutureAddress =
          vendorData.address_for_future_correspondence || {};

        Object.assign(row, {
          ...(vendorData.is_individual === "No" &&
            vendorData.individual_text && {
              CompanyName1: toUpperCase(vendorData.individual_text),
            }),
          ...(vendorData.is_individual !== "No" &&
            vendorData.surname && {
              Surname1: toUpperCase(vendorData.surname),
            }),
          ...(vendorData.is_individual !== "No" &&
            (vendorData.given_names || vendorData.other_names) && {
              GivenNames1: toUpperCase(
                vendorData.given_names || vendorData.other_names
              ),
            }),
          ...(vendorData.acn && { ACNNumber1: vendorData.acn }),
          ...(vendorData.email && { Email1: vendorData.email }),
          ...((vendorData.phone || vendorData.phone_number) && {
            PhoneNumber1: vendorData.phone || vendorData.phone_number,
          }),
          ...((vendorAddress.flat_unit_number || vendorAddress.flatunit) && {
            FlatUnitNumber1:
              vendorAddress.flat_unit_number || vendorAddress.flatunit,
          }),
          ...(vendorAddress.street_number && {
            StreetNumber1: vendorAddress.street_number,
          }),
          ...(vendorAddress.street_name && {
            StreetName1: toUpperCase(vendorAddress.street_name),
          }),
          ...(vendorAddress.street_type && {
            StreetType1: toUpperCase(vendorAddress.street_type),
          }),
          ...(vendorAddress.suburb && {
            Suburb1: toUpperCase(vendorAddress.suburb),
          }),
          ...(vendorAddress.postcode && { Postcode1: vendorAddress.postcode }),
          ...(vendorAddress.state && {
            State1: vendorAddress.state,
            Country1: getCountry(vendorAddress.state),
          }),
          ...((vendorFutureAddress.flat_unit_number ||
            vendorFutureAddress.flatunit) && {
            FlatUnitNumber2:
              vendorFutureAddress.flat_unit_number ||
              vendorFutureAddress.flatunit,
          }),
          ...(vendorFutureAddress.street_number && {
            StreetNumber2: vendorFutureAddress.street_number,
          }),
          ...(vendorFutureAddress.street_name && {
            StreetName2: toUpperCase(vendorFutureAddress.street_name),
          }),
          ...(vendorFutureAddress.street_type && {
            StreetType2: toUpperCase(vendorFutureAddress.street_type),
          }),
          ...(vendorFutureAddress.suburb && {
            Suburb2: toUpperCase(vendorFutureAddress.suburb),
          }),
          ...(vendorFutureAddress.postcode && {
            Postcode2: vendorFutureAddress.postcode,
          }),
          ...(vendorFutureAddress.state && {
            State2: vendorFutureAddress.state,
            Country2: getCountry(vendorFutureAddress.state),
          }),
          ...(vendorData.principal_place_of_residence && {
            PrincipalPlaceOfResidence: vendorData.principal_place_of_residence,
          }),
        });
      }

      if (purchaserData) {
        const purchaserAddress = purchaserData.address || {};
        const purchaserFutureAddress =
          purchaserData.address_for_future_correspondence || {};

        Object.assign(row, {
          ...(purchaserData.is_individual === "No" &&
            purchaserData.individual_text && {
              CompanyName2: toUpperCase(purchaserData.individual_text),
            }),
          ...(purchaserData.is_individual !== "No" &&
            purchaserData.surname && {
              Surname2: toUpperCase(purchaserData.surname),
            }),
          ...(purchaserData.is_individual !== "No" &&
            (purchaserData.given_names || purchaserData.other_names) && {
              GivenNames2: toUpperCase(
                purchaserData.given_names || purchaserData.other_names
              ),
            }),
          ...(purchaserData.acn && { ACNNumber2: purchaserData.acn }),
          ...(purchaserData.email && { Email2: purchaserData.email }),
          ...((purchaserData.phone || purchaserData.phone_number) && {
            PhoneNumber2: purchaserData.phone || purchaserData.phone_number,
          }),
          ...(purchaserData.date_of_birth && {
            DateOfBirth2: formatDate(purchaserData.date_of_birth),
          }),
          ...((purchaserAddress.flat_unit_number ||
            purchaserAddress.flatunit) && {
            FlatUnitNumber3:
              purchaserAddress.flat_unit_number || purchaserAddress.flatunit,
          }),
          ...(purchaserAddress.street_number && {
            StreetNumber3: purchaserAddress.street_number,
          }),
          ...(purchaserAddress.street_name && {
            StreetName3: toUpperCase(purchaserAddress.street_name),
          }),
          ...(purchaserAddress.street_type && {
            StreetType3: toUpperCase(purchaserAddress.street_type),
          }),
          ...(purchaserAddress.suburb && {
            Suburb3: toUpperCase(purchaserAddress.suburb),
          }),
          ...(purchaserAddress.postcode && { Postcode3: purchaserAddress.postcode }),
          ...(purchaserAddress.state && {
            State3: purchaserAddress.state,
            Country3: getCountry(purchaserAddress.state),
          }),
          ...((purchaserFutureAddress.flat_unit_number ||
            purchaserFutureAddress.flatunit) && {
            FlatUnitNumber4:
              purchaserFutureAddress.flat_unit_number ||
              purchaserFutureAddress.flatunit,
          }),
          ...(purchaserFutureAddress.street_number && {
            StreetNumber4: purchaserFutureAddress.street_number,
          }),
          ...(purchaserFutureAddress.street_name && {
            StreetName4: toUpperCase(purchaserFutureAddress.street_name),
          }),
          ...(purchaserFutureAddress.street_type && {
            StreetType4: toUpperCase(purchaserFutureAddress.street_type),
          }),
          ...(purchaserFutureAddress.suburb && {
            Suburb4: toUpperCase(purchaserFutureAddress.suburb),
          }),
          ...(purchaserFutureAddress.postcode && {
            Postcode4: purchaserFutureAddress.postcode,
          }),
          ...(purchaserFutureAddress.state && {
            State4: purchaserFutureAddress.state,
            Country4: getCountry(purchaserFutureAddress.state),
          }),
          ...(purchaserData.principal_place_of_residence && {
            PrincipalPlaceOfResidence:
              purchaserData.principal_place_of_residence,
          }),
        });
      }

      return row;
    };

    // Handle transferees and transferors, accounting for both singular and plural keys
    const transferors = pdfData.transferors || pdfData.transferee || [];
    const transferees = pdfData.transferees || pdfData.transferee || [];

    if (transferors.length === 1 && transferees.length === 1) {
      rows.push(createRow(transferors[0], transferees[0]));
    } else {
      transferors.forEach((transferor: any) => {
        rows.push(createRow(transferor, null));
      });
      transferees.forEach((transferee: any) => {
        rows.push(createRow(null, transferee));
      });
    }

    if (rows.length === 0) {
      rows.push(createRow(null, null));
    }
  });

  return rows;
};
  useEffect(() => {
    if (extractedIdData?.files) {
      setTotalFiles(extractedIdData.files.length);
      if (extractedIdData.files[currentFileIndex]) {
        const transformedRows = transformDataToRows(
          extractedIdData.files[currentFileIndex]
        );
        setRows(transformedRows);
      }
    }
  }, [extractedIdData, currentFileIndex]);

  const handleNextFile = () => {
    if (currentFileIndex < totalFiles - 1) {
      setCurrentFileIndex(currentFileIndex + 1);
      setPage(0);
    }
  };

  const handlePrevFile = () => {
    if (currentFileIndex > 0) {
      setCurrentFileIndex(currentFileIndex - 1);
      setPage(0);
    }
  };

  const handleDownloadCSV = async () => {
    if (!extractedIdData?.files) return;

    const allRows = extractedIdData.files.flatMap((file: any) => {
      return transformDataToRows(file);
    });
    const groupRow = sewColumns.map((col) => `"${col.group ?? ""}"`).join(",");
    const headerRow = sewColumns.map((col) => `"${col.label}"`).join(",");
    const dataRows = allRows.map((row: { [key: string]: any }) =>
      sewColumns.map((col) => `"${row[col.id] ?? ""}"`).join(",")
    );
    const csvContent = [groupRow, headerRow, ...dataRows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    saveAs(blob, `Job_${id}_All_Files_Export.csv`);
  };

  const handleChangePage = (_: unknown, newPage: number) => setPage(newPage);

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  return {
    state,
    extractedData,
    extractedIdData,
    extracteDataLoading,
    extractedPopupLoading,
    handleSearchInputChange,
    handlePageChange,
    handlePageSizeChange,
    onFormModalHandle,
    handleFormSubmit,
    handleDownloadCSV,
    rows,
    page,
    rowsPerPage,
    editingRow,
    editedRowData,
    handleRowClick,
    handleCellChange,
    handleSave,
    handleCancel,
    handleChangePage,
    handleChangeRowsPerPage,
    currentFileIndex,
    totalFiles,
    clientData,
    jobDatas,
    handleNextFile,
    handlePrevFile,
    handleClientChange,
    handleJobTypeChange,
    handleDateChange,
  };
};

export const useGetExtracteData = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: ["extracted-data", params],
    queryFn: () =>
      extracteDataService.getExtracteData(
        params.page,
        params.limit,
        params.searchTerm,
        params.clientId,
        params.jobTypeId,
        params.startDate
      ),
  });
};

export const useGetExtracteIdData = (id: string | number | undefined) => {
  return useQuery({
    queryKey: ["extracted-data", id],
    queryFn: () => {
      if (!id) return Promise.resolve(null);
      return extracteDataService.getExtracteDataById(id);
    },
    enabled: !!id,
  });
};

export const useUpdateExtractedData = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: any) =>
      extracteDataService.updateExtractedData(id, data),
    onSuccess: (_, variables: { id: string | number }) => {
      queryClient.invalidateQueries({
        queryKey: ["extracted-data", variables.id],
      });
    },
  });
};

export const useGetClients = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: ["clients", params],
    queryFn: () =>
      clientService.getClients(params.page, params.limit, params.searchTerm),
  });
};

export const useGetJobs = (params: Record<string, any> = {}) =>
  useQuery({
    queryKey: ["jobs", params],
    refetchInterval: 5000,
    queryFn: () =>
      jobService.getJobs(params.page, params.limit, params.searchTerm),
  });
