"use client";
import { useReducer, useCallback } from "react";
import { ClientProps } from "@/page-components/client/types";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import clientService from "@/services/clientService/client.Services";

type InitialStateProps = {
  searchText: string;
  page: number;
  pageSize: number;
  isFormModalOpen: boolean;
  selectedClients: any;
  sortField: string | null;
  sortOrder: "asc" | "desc" | null;
  clients: any[];
};

interface StateAction {
  type:
    | "SEARCH_TEXT"
    | "PAGE"
    | "PAGE_SIZE"
    | "FORM_MODAL_OPEN"
    | "SELECTED_CLIENTS"
    | "ADD_CLIENT"
    | "UPDATE_CLIENT"
    | "SORT_FIELD"
    | "SORT_ORDER";
  payload?: any;
}

const initialState: InitialStateProps = {
  searchText: "",
  page: 1,
  pageSize: 10,
  isFormModalOpen: false,
  selectedClients: null,
  sortField: null,
  sortOrder: null,
  clients: [],
};

const reducer = (state: InitialStateProps, action: StateAction) => {
  switch (action.type) {
    case "SEARCH_TEXT":
      return { ...state, searchText: action.payload };
    case "PAGE":
      return { ...state, page: action.payload };
    case "PAGE_SIZE":
      return { ...state, pageSize: action.payload };
    case "FORM_MODAL_OPEN":
      return { ...state, isFormModalOpen: action.payload };
    case "SELECTED_CLIENTS":
      return { ...state, selectedClients: action.payload };
    case "SORT_FIELD":
      return { ...state, sortField: action.payload };
    case "SORT_ORDER":
      return { ...state, sortOrder: action.payload };
    case "ADD_CLIENT":
      return { ...state, clients: [...state.clients, action.payload] };
    case "UPDATE_CLIENT":
      return {
        ...state,
        clients: state.clients.map((client) =>
          client.id === action.payload.id ? action.payload : client
        ),
      };
    default:
      return state;
  }
};

export const useClients = () => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const queryClient = useQueryClient();

  const clientQueryParams = {
    page: state.page,
    limit: state.pageSize,
    searchTerm: state.searchText,
  };

  const { data: clientData, isLoading: clientDataLoading } = useGetClients(
    clientQueryParams
  ) as any;

  const createClientMutation = useCreateClient();
  const updateClientMutation = useUpdateClient();

  const handleFormSubmit = async (clientData: ClientProps) => {
    try {
      if (clientData.id) {
        await updateClientMutation.mutateAsync({
          id: clientData.id,
          data: clientData,
        });
        dispatch({ type: "UPDATE_CLIENT", payload: clientData });
      } else {
        const createdClient = await createClientMutation.mutateAsync(
          clientData
        );
        dispatch({ type: "ADD_CLIENT", payload: createdClient });
      }
      queryClient.invalidateQueries({ queryKey: ["clients"] });
    } catch (error) {
      console.error("Error saving client:", error);
    }
  };

  const handleSearchInputChange = useCallback((query: string) => {
    dispatch({ type: "SEARCH_TEXT", payload: query });
  }, []);

  const handlePageChange = useCallback((newPage: number) => {
    dispatch({ type: "PAGE", payload: newPage });
  }, []);

  const handlePageSizeChange = useCallback((newPageSize: number) => {
    dispatch({ type: "PAGE_SIZE", payload: newPageSize });
    dispatch({ type: "PAGE", payload: 1 });
  }, []);

  const onFormModalHandle = useCallback(
    (type?: string, clients?: any) => {
      if (type === "add" || type === "edit" || type === "delete") {
        dispatch({
          type: "SELECTED_CLIENTS",
          payload: { type, ...clients },
        });
      } else {
        dispatch({ type: "SELECTED_CLIENTS", payload: null });
      }
      dispatch({ type: "FORM_MODAL_OPEN", payload: !state.isFormModalOpen });
    },
    [state.isFormModalOpen]
  );

  return {
    state,
    clientData,
    clientDataLoading,
    handleSearchInputChange,
    handlePageChange,
    handlePageSizeChange,
    onFormModalHandle,
    handleFormSubmit,
  };
};

export const useGetClients = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: ["clients", params],
    queryFn: () =>
      clientService.getClients(params.page, params.limit, params.searchTerm),
  });
};

export const useCreateClient = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: ClientProps) => clientService.createClients(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
    },
    onError: (error) => {
      console.error("Failed to create client:", error);
    },
  });
};

export const useUpdateClient = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: number | string; data: any }) =>
      clientService.updateClients(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
    },
    onError: (error) => {
      console.error("Failed to update client:", error);
    },
  });
};

export const useDeleteClients = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => clientService.deleteClients(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clients"] });
    },
  });
};
