"use client";

import loginService from "@/services/login/login.service";
import { UserLogin } from "@/services/login/type";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useCallback, useReducer } from "react";
import { toast } from "react-toastify";
import Cookies from "js-cookie";
import { useUserContext } from "@/context/userContext/UserContext";

export interface UserProps {
  api_token: string;
  name: string;
  email: string;
}

export type InitialStateProps = {
  showPassword: boolean;
};

export interface StateAction {
  type: "TOGGLE_PASSWORD_VISIBILITY";
  payload?: boolean;
}

const initialState: InitialStateProps = {
  showPassword: false,
};

const reducer = (
  state: InitialStateProps,
  action: StateAction
): InitialStateProps => {
  switch (action.type) {
    case "TOGGLE_PASSWORD_VISIBILITY":
      return { ...state, showPassword: action.payload ?? !state.showPassword };
    default:
      return state;
  }
};

export const useSignInForm = () => {
  const router = useRouter();
  const [state, dispatch] = useReducer(reducer, initialState);
  const { showPassword } = state;

  const { mutate, isPending } = useCreateLogin();
  const { setUser } = useUserContext();
  const onSubmit = useCallback(
    (data: UserLogin) => {
      mutate(data, {
        onSuccess: (response: any) => {
          const {
            data: { user },
          } = response as {
            data: { user: any };
          };

          const { email, api_token, name } = user as any;

          if (api_token) {
            const expirationTime = new Date(
              Date.now() + 24 * 60 * 60 * 1000
            ).toISOString();

            Cookies.set("token", api_token, { expires: 1 });
            Cookies.set("tokenExpiration", expirationTime, { expires: 1 });

            localStorage.setItem("user", JSON.stringify(user));
            setUser(user);

            toast.success(response?.message || "Login successful");
            router.push("/dashboard");
          }
        },
        onError: (error: any) => {
          const errorMessage =
            error?.response?.data?.message ||
            error?.message ||
            "An error occurred. Please try again.";

          toast.error(errorMessage);
        },
      });
    },
    [mutate, router, setUser]
  );

  const togglePasswordVisibility = useCallback(() => {
    dispatch({ type: "TOGGLE_PASSWORD_VISIBILITY", payload: !showPassword });
  }, [showPassword]);

  return {
    showPassword,
    isPending,
    onSubmit,
    togglePasswordVisibility,
  };
};

export const useCreateLogin = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: UserLogin) => loginService.createLogin(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["login"] });
    },
  });
};
