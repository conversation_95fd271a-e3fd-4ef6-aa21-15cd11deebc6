import { queryClient } from "@/lib/queryClient";
import usersService from "@/services/userService/user.service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useReducer } from "react";

type InitialStateProps = {
  searchText: string;
  page: number;
  pageSize: number;
  isFormModalOpen: boolean;
  selectedUser: any;
  sortField: string | null;
  sortOrder: "asc" | "desc" | null;
};

interface StateAction {
  type:
    | "SEARCH_TEXT"
    | "PAGE"
    | "PAGE_SIZE"
    | "FORM_MODAL_OPEN"
    | "SELECTED_USER"
    | "SORT_FIELD"
    | "SORT_ORDER"
    | "CLEAR";
  payload?: any;
}

const initialState: InitialStateProps = {
  searchText: "",
  page: 1,
  pageSize: 10,
  isFormModalOpen: false,
  selectedUser: null,
  sortField: null,
  sortOrder: null,
};

const reducer = (state: InitialStateProps, action: StateAction) => {
  switch (action.type) {
    case "SEARCH_TEXT":
      return { ...state, searchText: action.payload };
    case "PAGE":
      return { ...state, page: action.payload };
    case "PAGE_SIZE":
      return { ...state, pageSize: action.payload };
    case "FORM_MODAL_OPEN":
      return { ...state, isFormModalOpen: action.payload };
    case "SELECTED_USER":
      return { ...state, selectedUser: action.payload };
    case "SORT_FIELD":
      return { ...state, sortField: action.payload };
    case "SORT_ORDER":
      return { ...state, sortOrder: action.payload };
    default:
      return state;
  }
};

export const useUserHooks = () => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const userParams = {
    page: state.page,
    limit: state.pageSize,
    searchTerm: state.searchText,
  };

  const { data } = useGetUsers() as any;
  const userData = data?.data;

  const handleSearchInputChange = useCallback((query: string) => {
    dispatch({ type: "SEARCH_TEXT", payload: query });
  }, []);

  const handlePageChange = useCallback((page: number) => {
    dispatch({ type: "PAGE", payload: page });
  }, []);

  const handlePageSizeChange = useCallback((page: number) => {
    dispatch({ type: "PAGE_SIZE", payload: page });
    dispatch({ type: "PAGE", payload: 1 });
  }, []);

  const onFormModalHandle = useCallback(
    (type?: string, user?: any) => {
      if (
        type === "add" ||
        type === "edit" ||
        type === "delete" ||
        type === "view"
      ) {
        dispatch({
          type: "SELECTED_USER",
          payload: { type, user: user || null },
        });
      } else {
        dispatch({ type: "SELECTED_USER", payload: null });
      }
      dispatch({ type: "FORM_MODAL_OPEN", payload: !state.isFormModalOpen });
    },
    [state.isFormModalOpen]
  );

  const { mutate: handleFormSubmit } = useMutation({
    mutationFn: (data: any) => {
      if (state.selectedUser?.type === "edit") {
        return usersService.updateUsers(state.selectedUser.user.id, data);
      }
      return usersService.createUsers(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      dispatch({ type: "FORM_MODAL_OPEN", payload: false });
    },
    onError: (error) => {
      console.error("Error submitting form:", error);
    },
  });

  const handleFormSubmitWrapper = useCallback(
    (formData: any) => {
      const payload = {
        ...formData,
      };
      handleFormSubmit(payload);
    },
    [handleFormSubmit]
  );

  const handleSortModelChange = useCallback((sortModel: any) => {
    if (sortModel.length > 0) {
      const sortField = sortModel[0]?.field;
      const sortOrder = sortModel[0]?.sort;
      dispatch({ type: "SORT_FIELD", payload: sortField });
      dispatch({ type: "SORT_ORDER", payload: sortOrder });
    }
  }, []);

  return {
    state,
    userData,
    handleSearchInputChange,
    handlePageChange,
    handlePageSizeChange,
    onFormModalHandle,
    handleFormSubmit,
    handleFormSubmitWrapper,
    handleSortModelChange,
  };
};

export const useGetUsers = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: ["users", params],
    queryFn: () =>
      usersService.getUsers(params.page, params.limit, params.searchTerm),
    placeholderData: (previousData) => previousData,
  });
};

// export const useUserId = (id: string | number) => {
//   return useQuery({
//     queryKey: ["users", id],
//     queryFn: () => usersService.getUserById(id),
//     enabled: !!id,
//   });
// };

export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string | number; data: any }) =>
      usersService.updateUsers(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string | number) => usersService.deleteUsers(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};
