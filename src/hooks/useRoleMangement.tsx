import { RoleManagementProps } from "@/page-components/role-management/types";
import roleUserService from "@/services/roleUsersService/roleUserService";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useReducer, useCallback } from "react";

type InitialStateProps = {
  searchText: string;
  page: number;
  pageSize: number;
  isFormModalOpen: boolean;
  selectedRoles: any;
  sortField: string | null;
  sortOrder: "asc" | "desc" | null;
  roleMangement: any[];
};

interface StateAction {
  type:
    | "SEARCH_TEXT"
    | "PAGE"
    | "PAGE_SIZE"
    | "FORM_MODAL_OPEN"
    | "SELECTED_CLIENTS"
    | "ADD_CLIENT"
    | "UPDATE_CLIENT"
    | "SORT_FIELD"
    | "SORT_ORDER";
  payload?: any;
}

const initialState: InitialStateProps = {
  searchText: "",
  page: 1,
  pageSize: 10,
  isFormModalOpen: false,
  selectedRoles: null,
  sortField: null,
  sortOrder: null,
  roleMangement: [],
};

const reducer = (state: InitialStateProps, action: StateAction) => {
  switch (action.type) {
    case "SEARCH_TEXT":
      return { ...state, searchText: action.payload };
    case "PAGE":
      return { ...state, page: action.payload };
    case "PAGE_SIZE":
      return { ...state, pageSize: action.payload, page: 1 };
    case "FORM_MODAL_OPEN":
      return { ...state, isFormModalOpen: action.payload };
    case "SELECTED_CLIENTS":
      return { ...state, selectedRoles: action.payload };
    case "SORT_FIELD":
      return { ...state, sortField: action.payload };
    case "SORT_ORDER":
      return { ...state, sortOrder: action.payload };
    case "ADD_CLIENT":
      return {
        ...state,
        roleMangement: [...state.roleMangement, action.payload],
      };
    case "UPDATE_CLIENT":
      return {
        ...state,
        clients: state.roleMangement.map((roleMangement) =>
          roleMangement.id === action.payload.id
            ? action.payload
            : roleMangement
        ),
      };
    default:
      return state;
  }
};

export const useRoleManagement = () => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const roleParams = {
    page: state.page,
    limit: state.pageSize,
    searchTerm: state.searchText,
  };

  const { data: roleUsersData, isLoading: roleDataLoading } = useGetRoleUsers(
    roleParams
  ) as any;

  const addRoleUsers = useCreateRoleUsers();
  const UpdateRoleUsers = useUpdateRoleUsers();

  const handleFormSubmit = async (roleManagementData: RoleManagementProps) => {
    try {
      if (roleManagementData.id) {
        const updated = await UpdateRoleUsers.mutateAsync({
          id: roleManagementData.id,
          data: roleManagementData,
        });
        dispatch({ type: "UPDATE_CLIENT", payload: updated });
      } else {
        const created = await addRoleUsers.mutateAsync(roleManagementData);
        dispatch({ type: "ADD_CLIENT", payload: created });
      }
    } catch (error) {
      console.error("Error saving role user:", error);
    }
  };

  const handleSearchInputChange = useCallback((query: string) => {
    dispatch({ type: "SEARCH_TEXT", payload: query });
  }, []);

  const handlePageChange = useCallback((page: number) => {
    dispatch({ type: "PAGE", payload: page });
  }, []);

  const handlePageSizeChange = useCallback((size: number) => {
    dispatch({ type: "PAGE_SIZE", payload: size });
  }, []);

  const onFormModalHandle = useCallback(
    (type?: string, roleManagement?: any) => {
      if (type === "add" || type === "edit" || type === "delete") {
        dispatch({
          type: "SELECTED_CLIENTS",
          payload: { type, roleManagement: roleManagement || null },
        });
      } else {
        dispatch({ type: "SELECTED_CLIENTS", payload: null });
      }
      dispatch({ type: "FORM_MODAL_OPEN", payload: !state.isFormModalOpen });
    },
    [state.isFormModalOpen]
  );

  return {
    state,
    roleUsersData,
    roleDataLoading,
    handleSearchInputChange,
    handlePageChange,
    handlePageSizeChange,
    onFormModalHandle,
    handleFormSubmit,
  };
};

export const useGetRoleUsers = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: ["users", params],
    queryFn: () =>
      roleUserService.getRoleUsers(
        params.page,
        params.limit,
        params.searchTerm
      ),
  });
};

export const useCreateRoleUsers = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: RoleManagementProps) =>
      roleUserService.createRoleUsers(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error) => {
      console.error("Failed to create client:", error);
    },
  });
};

export const useUpdateRoleUsers = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: number | string; data: any }) =>
      roleUserService.updateRoleUsers(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error) => {
      console.error("Failed to update users:", error);
    },
  });
};

export const useDeleteRole = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string | number) => roleUserService.deleteRoleUsers(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};
