"use client";

import React from "react";
import { AppRouterCacheProvider } from "@mui/material-nextjs/v15-appRouter";
import ThemeProvider from "@mui/material/styles/ThemeProvider";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { theme } from "@/theme/theme";
import BreakpointsProvider from "./BreakpointsProvider";
import { CssBaseline } from "@mui/material";
import AppUseContext from "@/context/AppUseContext";

const AppProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <AppRouterCacheProvider>
      <ThemeProvider theme={theme}>
        <BreakpointsProvider>
          <CssBaseline />
          <QueryClientProvider client={queryClient}>
            <AppUseContext>{children}</AppUseContext>
          </QueryClientProvider>
        </BreakpointsProvider>
      </ThemeProvider>
    </AppRouterCacheProvider>
  );
};

export default AppProviders;
