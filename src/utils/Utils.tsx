
export const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toISOString().split("T")[0]; // YYYY-MM-DD format
  };
  
  /**
 * Generate a URL with query parameters from a base URL and parameters object.
 * @param {string} baseUrl - The base URL.
 * @param {Record<string, any>} params - An object containing query parameters.
 * @returns {string} - The complete URL with encoded query parameters.
 */
  export const generateUrlWithParams = (baseUrl: string, params?: Record<string, any>): string => {
    const filteredParams = params
      ? Object.entries(params)
          .filter(([_, value]) => value !== null && value !== undefined) // Exclude null or undefined values
          .reduce((acc, [key, value]) => {
            acc[key] = value;
            return acc;
          }, {} as Record<string, any>)
      : {};
  
    return `${baseUrl}?${new URLSearchParams(filteredParams).toString()}`;
  };
  

export const currentDate = new Date().toISOString().split("T")[0];