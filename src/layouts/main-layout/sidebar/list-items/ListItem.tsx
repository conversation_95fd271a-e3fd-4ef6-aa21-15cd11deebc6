
import Stack from "@mui/material/Stack";
import sitemap from "@/config/siteMap";
import List from "@mui/material/List";
import DrawerItems from "../DrawerItems";
import { fontFamily } from "@/theme/typography";
import { ButtonBase, Typography } from "@mui/material";
import Link from "next/link";

const ListItem = () => {
  return (
    <>
    <Stack
        pt={5}
        pb={4.5}
        px={4.5}
        justifyContent="flex-start"
        position="sticky"
        top={0}
        borderBottom={1}
        borderColor="info.main"
        bgcolor="info.lighter"
        zIndex={1000}
      >
        <ButtonBase component={Link} href="/" disableRipple>
          <Typography
            variant="h3"
            textTransform="uppercase"
            letterSpacing={1}
            fontFamily={fontFamily.poppins}
          >
            SundocX
          </Typography>
        </ButtonBase>
      </Stack>
    <Stack
      mb={1}
      alignItems="center"
      justifyContent="space-between"
    >
        <List>
          {sitemap.map((drawerItem) => (
            <DrawerItems key={drawerItem.id} item={drawerItem}/>
          ))}
        </List>
    </Stack>
    </>
  );
};

export default ListItem;
