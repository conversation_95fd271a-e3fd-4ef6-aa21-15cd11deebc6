import Link from "@mui/material/Link";
import List from "@mui/material/List";
import Collapse from "@mui/material/Collapse";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import ListItemButton from "@mui/material/ListItemButton";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import { SubItem } from "@/config/siteMap";
import { usePathname } from "next/navigation";

interface CollapsedItemProps {
  subItems: SubItem[] | undefined;
  open: boolean;
  parentActive: boolean;
}

const CollapseListItem = ({ subItems, open }: CollapsedItemProps) => {
  const pathname = usePathname();

  return (
    <Collapse in={open} timeout="auto">
      <List component="div" disablePadding>
        {subItems?.map((subItem, index) => {
          const uniqueKey = subItem.id || `subItem-${index}`;
          const isActive = pathname === subItem.path;

          return (
            <ListItemButton
              key={uniqueKey}
              selected={isActive}
              component={subItem.path ? Link : "div"}
              href={subItem.path}
              sx={{
                "&.Mui-selected": {
                  backgroundColor: "#4318FF",
                  color: isActive ? "white" : "blue",
                  "&:hover": {
                    backgroundColor: "#4318FF",
                  },
                },
              }}
            >
              <ListItemIcon
                sx={{
                  color: isActive ? "white" : "#2b3674",
                }}
              >
                {subItem.icon && (
                  <IconifyIcon icon={subItem.icon} fontSize="h6.fontSize" />
                )}
              </ListItemIcon>
              <ListItemText
                primary={subItem.title}
                sx={{
                  "& .MuiTypography-root": {
                    color: isActive ? "white" : "#2b3674",
                  },
                }}
              />
            </ListItemButton>
          );
        })}
      </List>
    </Collapse>
  );
};

export default CollapseListItem;
