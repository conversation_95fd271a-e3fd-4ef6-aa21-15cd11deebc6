import Link from "@mui/material/Link";
import CollapseListItem from "./list-items/CollapseListItem";
import { DrawerItem } from "@/config/siteMap";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import {
  ListItemButton,
  ListItemIcon,
  ListItemText,
  ListItem,
} from "@mui/material";
import { useState } from "react";
import { usePathname } from "next/navigation";

const DrawerItems = ({ item }: { item: DrawerItem }) => {
  const [open, setOpen] = useState(false);
  const pathName = usePathname();

  const handleCollapsedItem = () => setOpen(!open);

  const { title, path, icon, collapsible, subList } = item;

  const isActive = path === pathName;
  const isParentActive = subList?.some((subItem) => subItem.path === pathName);

  return (
    <ListItem
      disablePadding
      sx={{
        flexDirection: "column",
        alignItems: "stretch",
        mb: 1.25,
        width: 274,
      }}
    >
      <ListItemButton
        selected={isActive}
        onClick={handleCollapsedItem}
        component={path ? Link : "div"}
        href={path}
        sx={{
          "&.Mui-selected": {
            backgroundColor: "#4318FF",
            "&:hover": {
              backgroundColor: "#4318FF",
            },
          },
        }}
      >
        <ListItemIcon
          sx={{
            color: isActive ? "white" : "#2b3674",
          }}
        >
          {icon && <IconifyIcon icon={icon} fontSize="h4.fontSize" />}
        </ListItemIcon>
        <ListItemText
          primary={title}
          sx={{
            "& .MuiTypography-root": {
              color: isActive ? "white" : "#2b3674",
            },
          }}
        />

        {collapsible && (
          <IconifyIcon
            icon={open ? "ep:arrow-up" : "ep:arrow-down"}
            sx={{
              color: isActive ? "white" : "#2b3674",
            }}
          />
        )}
      </ListItemButton>
      {collapsible && (
        <CollapseListItem
          subItems={subList}
          open={open}
          parentActive={!!(isActive || isParentActive)}
        />
      )}
    </ListItem>
  );
};

export default DrawerItems;
