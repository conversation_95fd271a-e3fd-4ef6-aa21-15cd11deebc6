import { useState } from "react";
import Box from "@mui/material/Box";
import Menu from "@mui/material/Menu";
import Stack from "@mui/material/Stack";
import Avatar from "@mui/material/Avatar";
import Divider from "@mui/material/Divider";
import MenuItem from "@mui/material/MenuItem";
import Typography from "@mui/material/Typography";
import ButtonBase from "@mui/material/ButtonBase";
import ListItemIcon from "@mui/material/ListItemIcon";
import IconifyIcon from "@/core-components/base/IconifyIcon";

interface MenuItems {
  id: number;
  title: string;
  icon: string;
}

const menuItems: MenuItems[] = [
  {
    id: 1,
    title: "View Profile",
    icon: "material-symbols:account-circle-outline",
  },
  {
    id: 2,
    title: "Logout",
    icon: "material-symbols:logout",
  },
];

const ProfileMenu = ({ userImage }: any) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleProfileClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };
  const userData = typeof window !== "undefined" ? JSON.parse(localStorage.getItem("user") || "{}") : {};
  const profileName = userData.name;
  const profileEmail = userData.email;

  const handleLogout = () => {

    localStorage.removeItem("user");

    window.location.href = "/authentication/sign-in/";
  };
  return (
    <>
      <ButtonBase
        onClick={handleProfileClick}
        aria-controls={open ? "account-menu" : undefined}
        aria-expanded={open ? "true" : undefined}
        aria-haspopup="true"
        disableRipple
      >
        <Avatar src={userImage} alt="user-avatar">
        </Avatar>
      </ButtonBase>

      <Menu
        anchorEl={anchorEl}
        id="account-menu"
        open={open}
        onClose={handleProfileMenuClose}
        onClick={handleProfileMenuClose}
        sx={{
          mt: 1.5,
          "& .MuiList-root": {
            p: 0,
            width: 230,
          },
        }}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
      >
        <Box p={1}>
          <MenuItem
            onClick={handleProfileMenuClose}
            sx={{ "&:hover": { bgcolor: "info.dark" } }}
          >
            <Avatar
              src={userImage}
              alt="user-avatar"
              sx={{ mr: 1, height: 42, width: 42 }}
            >
            </Avatar>

            <Stack direction="column">
              <Typography variant="body2" color="text.primary" fontWeight={600}>
                {profileName}
              </Typography>
              <Typography
                variant="caption"
                color="text.secondary"
                fontWeight={400}
              >
                {profileEmail}
              </Typography>
            </Stack>
          </MenuItem>
        </Box>

        <Divider sx={{ my: 0 }} />

        <Box p={1}>
          {menuItems.map((item) => {
            return (
              <MenuItem
                key={item.id}
                onClick={
                  item.title === "Logout"
                    ? handleLogout
                    : handleProfileMenuClose
                }
                sx={{ py: 1 }}
              >
                <ListItemIcon
                  sx={{
                    mr: 1,
                    color: "text.secondary",
                    fontSize: "h5.fontSize",
                  }}
                >
                  <IconifyIcon icon={item.icon} />
                </ListItemIcon>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  fontWeight={500}
                >
                  {item.title}
                </Typography>
              </MenuItem>
            );
          })}
        </Box>
      </Menu>
    </>
  );
};

export default ProfileMenu;
