import Link from "next/link";
import Stack from "@mui/material/Stack";
import Toolbar from "@mui/material/Toolbar";
import ButtonBase from "@mui/material/ButtonBase";
import IconButton from "@mui/material/IconButton";
import ProfileMenu from "./ProfileMenu";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import { Typography } from "@mui/material";
import { getHeadingForRoute } from "@/config/tableHeadings";
import { usePathname } from "next/navigation";

interface TopbarProps {
  isClosing: boolean;
  mobileOpen: boolean;
  setMobileOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Topbar = ({ isClosing, mobileOpen, setMobileOpen }: TopbarProps) => {
  const handleDrawerToggle = () => {
    if (!isClosing) {
      setMobileOpen(!mobileOpen);
    }
  };

  const pathName = usePathname();
  const heading = getHeadingForRoute(pathName || "");

  const backRouteToCustomer = heading?.includes("Customer Profile");

  return (
    <Stack
      height={90}
      alignItems="center"
      justifyContent="space-between"
      bgcolor="transparent"
      zIndex={1200}
    >
      <Stack spacing={{ xs: 1.5, sm: 2 }} alignItems="center">
        <ButtonBase
          component={Link}
          href="/"
          disableRipple
          sx={{
            lineHeight: 0,
            display: { xs: "none", sm: "block", lg: "none" },
          }}
        >
          {/* <Image src={AppLogo as any} alt="logo" height={44} width={44} /> */}
        </ButtonBase>

        <Toolbar sx={{ display: { xm: "block", lg: "none" } }}>
          <IconButton
            size="large"
            edge="start"
            color="inherit"
            aria-label="menu"
            onClick={handleDrawerToggle}
          >
            <IconifyIcon icon="ic:baseline-menu" />
          </IconButton>
        </Toolbar>

        <Toolbar sx={{ display: { xm: "block", md: "none" } }}>
          <IconButton
            size="large"
            edge="start"
            color="inherit"
            aria-label="search"
          >
            <IconifyIcon icon="bx:search" />
          </IconButton>
        </Toolbar>

        {backRouteToCustomer && (
          <ButtonBase
            component={Link}
            href="/customers"
            disableRipple
            sx={{
              lineHeight: 0,
              display: { xs: "none", sm: "block", lg: "block" },
            }}
          >
            <IconifyIcon icon="ic:baseline-arrow-back" fontSize={30} />
          </ButtonBase>
        )}

        <Typography variant="h3">{heading}</Typography>
      </Stack>

      <Stack spacing={{ xs: 1.5, sm: 2 }} alignItems="center">
        <ProfileMenu />
      </Stack>
    </Stack>
  );
};

export default Topbar;
