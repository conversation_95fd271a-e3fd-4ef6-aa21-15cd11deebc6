"use client";

import { PropsWithChildren, useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import AuthLayout from "./auth-layout";
import MainLayout from "./main-layout";
import React from "react";
import { ToastContainer } from "react-toastify";

const LayoutWrapper = ({ children }: PropsWithChildren) => {
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    if (pathname === "/") {
      router.replace("/authentication/sign-in");
    }
  }, [pathname, router]);

  if (pathname.startsWith("/authentication")) {
    return (
      <AuthLayout>
        {children}
        <ToastContainer position="top-right" autoClose={3000} />
      </AuthLayout>
    );
  }

  return (
    <React.StrictMode>
      <MainLayout>{children}</MainLayout>
    </React.StrictMode>
  );
};

export default LayoutWrapper;
