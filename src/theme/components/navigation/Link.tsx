
import { Theme } from '@mui/material';
import { Components } from '@mui/material/styles/components';
import Link  from 'next/link';
import { forwardRef } from 'react';

const LinkBehavior = forwardRef<
  HTMLAnchorElement,
  Omit<React.ComponentProps<typeof Link>, 'href'> & { href: string }
>((props, ref) => {
  const { href, ...other } = props;
  return (
    <Link href={href} passHref {...other} />
  );
});

const MuiLink: Components<Omit<Theme, 'components'>>['MuiLink'] = {
  defaultProps: {
    underline: 'none',
    component: LinkBehavior,
  },
};

export default MuiLink;

