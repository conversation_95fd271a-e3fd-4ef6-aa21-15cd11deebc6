import echart from "@/theme/styles/echart";
import scrollbar from "@/theme/styles/scrollbar";
import { Theme } from "@mui/material";
import { Components } from "@mui/material/styles/components";

const CssBaseline: Components<Omit<Theme, "components">>["MuiCssBaseline"] = {
  styleOverrides: (theme) => ({
    "*, *::before, *::after": {
      margin: 0,
      padding: 0,
      boxSizing: "border-box",
    },
    html: {
      scrollBehavior: "smooth",
    },
    body: {
      fontVariantLigatures: "none",
      backgroundColor: theme.palette.info?.main || "#FFFFFF",
      ...scrollbar(theme),
    },
    ...echart(),
  }),
};

export default CssBaseline;

