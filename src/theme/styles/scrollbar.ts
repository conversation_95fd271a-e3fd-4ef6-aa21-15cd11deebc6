

const scrollbar = (theme: { palette: {  neutral: { light: any; }; }; }) => ({
  '*::-webkit-scrollbar': {
    width: 5,
    height: 5,
    WebkitAppearance: 'none',
    backgroundColor: 'transparent',
    visibility: 'hidden',
  },
  '*::-webkit-scrollbar-track': {
    margin: 0,
  },
  '*::-webkit-scrollbar-thumb': {
    borderRadius: 3,
    backgroundColor: theme.palette?.neutral.light,
    visibility: 'hidden',
  },
});

export default scrollbar;
