import { useEffect, useState } from "react";
import { useDocuments } from "@/hooks/useDocuments";
import { useGetTemplate } from "@/hooks/useTemplete";
import { useGetClients } from "@/hooks/useClient";

export const useFetchDocumentOptions = () => {
  const [documentOptions, setDocumentOptions] = useState();

  const { data } = useDocuments() as any;
  const documents = data?.data || [];

  useEffect(() => {
    if (Array.isArray(documents)) {
      const options: any = documents.map((doc: any) => ({
        value: doc.id,
        label: doc.file_name,
      }));

      setDocumentOptions(options);
    }
  }, [documents]);

  return documentOptions;
};

export const useFetchTemplateOptions = () => {
  const [templateOptions, setTemplateOptions] = useState([
    { value: "", label: "Select a template" },
  ]);

  const { data } = useGetTemplate() as any;
  const templates = data?.data || [];

  useEffect(() => {
    if (Array.isArray(templates)) {
      const options = templates.map((template: any) => ({
        value: template.id,
        label: template.name,
      }));

      setTemplateOptions([
        { value: "", label: "Select a template" },
        ...options,
      ]);
    }
  }, [templates]);

  return templateOptions;
};

export const useFetchClientOptions = () => {
  const [clientOptions, setClientOptions] = useState([
    { value: "", label: "Select a Client" },
  ]);

  const { data } = useGetClients() as any;
  const clientData = data?.data;

  useEffect(() => {
    if (Array.isArray(clientData)) {
      const options = clientData.map((template: any) => ({
        value: template.id,
        label: template.name,
      }));

      setClientOptions([{ value: "", label: "Select a Client" }, ...options]);
    }
  }, [clientData]);

  return clientOptions;
};
