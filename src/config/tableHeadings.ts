export const TableHeading: { [key: string]: string } = {
  "/management/documents": "Documents",
  "/management/templates": "Templates",
  "/": "Dashboard",
};

const dynamicRoutes = {
  "/customers/profile/": "Customer Profile",
};

export const getHeadingForRoute = (route: string): string | undefined => {
  for (const [dynamicRoute, heading] of Object.entries(dynamicRoutes)) {
    if (route.startsWith(dynamicRoute)) {
      return heading;
    }
  }
  return TableHeading[route];
};
