import paths, { rootPaths } from "./path";

interface Item {
  id: number;
  icon?: string;
  title: string;
  path?: string;
  active?: boolean;
}

export interface SubItem {
  id?: number;
  icon?: string;
  title: string;
  path?: string;
  active?: boolean;
}

export interface DrawerItem extends Item {
  collapsible: boolean;
  subList?: SubItem[];
}

const sitemap: DrawerItem[] = [
  {
    id: 1,
    title: "Dashboard",
    path: rootPaths.root,
    icon: "ic:round-home",
    collapsible: false,
    active: true,
  },
  {
    id: 2,
    title: "Documents Management",
    active: true,
    collapsible: true,
    icon: "fa:houzz",
    subList: [
      {
        icon: "ri:product-hunt-fill",
        title: "Documents",
        path: paths.documents,
        active: true,
      },
      // {
      //   icon: "material-symbols:folder",
      //   title: "Templates",
      //   path: paths.templates,
      //   active: true,
      // },
      {
        icon: "material-symbols:folder",
        title: "Job<PERSON>",
        path: paths.jobs,
        active: true,
      },
      {
        icon: "material-symbols:folder",
        title: "Extracted Data",
        path: paths.extracteDate,
        active: true,
      },
    ],
  },
  {
    id: 3,
    title: "Clients",
    active: true,
    path: paths.clients,
    icon: "mdi:users-group",
    collapsible: false,
  },
  {
    id: 4,
    title: "Role Management",
    active: true,
    path: paths.RoleManagement,
    icon: "mdi:users-group",
    collapsible: false,
  },
];

export default sitemap;
