import { Icon } from "@iconify/react/dist/iconify.js";

export const getButtonStyles = ({ isCompleted, isFailed, isTodo }: any) => {
  const baseStyles = {
    textTransform: "none",
    fontWeight: 600,
    fontSize: "0.75rem",
    borderRadius: "8px",
    minWidth: "70px",
    height: "28px",
    transition: "all 0.3s ease",
    boxShadow: "none",
    "&:hover": {
      transform: "translateY(-1px)",
      boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
    },
  };

  if (isCompleted) {
    return {
      ...baseStyles,
      backgroundColor: "transparent",
      border: "1px solid #e0e0e0",
      color: "#9e9e9e",
      "&:hover": {
        ...baseStyles["&:hover"],
        transform: "none",
        boxShadow: "none",
        cursor: "not-allowed",
      },
    };
  }

  if (isFailed) {
    return {
      ...baseStyles,
      backgroundColor: "#FA113D",
      color: "white",
      border: "1px solid #ffcdd2",
      "&:hover": {
        backgroundColor: "red",
        color: "white",
      },
    };
  }

  if (isTodo) {
    return {
      ...baseStyles,
      backgroundColor: "blue",
      color: "white",
      border: "1px solid #c8e6c9",
      "&:hover": {
        backgroundColor: "blue",
        color: "white",
      },
    };
  }

  // Default/Run state
  return {
    ...baseStyles,
    backgroundColor: "blue",
    color: "white",
    border: "1px solid #c8e6c9",
    "&:hover": {
      backgroundColor: "blue",
      color: "white",
    },
  };
};

export const getStatusConfig = (status: string) => {
  const config: any = {
    "In Progress": {
      bg: "rgba(2, 136, 209, 0.08)",
      text: "#0288D1",
      icon: (
        <Icon
          icon="mdi:progress-clock"
          width={16}
          style={{ marginRight: 8, color: "#0288D1" }}
        />
      ),
    },
    Completed: {
      bg: "rgba(56, 142, 60, 0.08)",
      text: "#388E3C",
      icon: (
        <Icon
          icon="mdi:check-circle-outline"
          width={16}
          style={{ marginRight: 8, color: "#388E3C" }}
        />
      ),
    },
    Failed: {
      bg: "rgba(211, 47, 47, 0.08)",
      text: "#D32F2F",
      icon: (
        <Icon
          icon="mdi:alert-circle-outline"
          width={16}
          style={{ marginRight: 8, color: "#D32F2F" }}
        />
      ),
    },
    Error: {
      bg: "rgba(211, 47, 47, 0.08)",
      text: "#D32F2F",
      icon: (
        <Icon
          icon="mdi:alert-circle-outline"
          width={16}
          style={{ marginRight: 8, color: "#D32F2F" }}
        />
      ),
    },
    "To do": {
      bg: "rgba(255, 160, 0, 0.08)",
      text: "#FFA000",
      icon: (
        <Icon
          icon="mdi:clock-outline"
          width={16}
          style={{ marginRight: 8, color: "#FFA000" }}
        />
      ),
    },
    default: {
      bg: "rgba(97, 97, 97, 0.08)",
      text: "#616161",
      icon: (
        <Icon
          icon="mdi:help-circle-outline"
          width={16}
          style={{ marginRight: 8, color: "#616161" }}
        />
      ),
    },
  };

  return config[status] || config.default;
};

export const getFileIcon = (type: string) => {
  switch (type?.toLowerCase()) {
    case "pdf":
      return { icon: "mdi:file-pdf-box", color: "#E53935" };
    case "docx":
    case "doc":
      return { icon: "mdi:file-word-box", color: "#1E88E5" };
    case "xlsx":
    case "xls":
      return { icon: "mdi:file-excel-box", color: "#43A047" };
    case "jpg":
    case "png":
      return { icon: "mdi:file-image", color: "#FB8C00" };
    default:
      return { icon: "mdi:file-document-outline", color: "#6D4C41" };
  }
};
