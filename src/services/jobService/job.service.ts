import { apiService } from "@/services/api";
import { JobsProps } from "@/page-components/jobs/types";

const jobService = {
  getJobs: (
    page?: number,
    limit?: number,
    searchTerm?: string,
    clientId?: number,
    jobTypeId?: number,
    userId?: number
  ) => {
    return apiService.get<{ data: JobsProps[] }>(
      `/jobs?page=${page}&limit=${limit}&searchTerm=${searchTerm}&clientId=${clientId}&jobTypeId=${jobTypeId}&userId=${userId}`
    );
  },
  getJobsById: (id: string | number) =>
    apiService.get<{ data: JobsProps }>(`/jobs/${id}`),

  createJobs: (data: Omit<JobsProps, "id">) =>
    apiService.post<{ data: JobsProps }>("/jobs", data),

  updateJobs: (id: string | number, data: Partial<JobsProps>) =>
    apiService.put<{ data: JobsProps }>(`/jobs/${id}`, data),

  deleteJobs: (id: string | number) => apiService.delete<void>(`/jobs/${id}`),
};

export default jobService;
