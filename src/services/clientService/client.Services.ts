// services/documentService.ts
import { apiService } from "../api";

interface Clients {}

const clientService = {
  getClients: (page?: number, limit?: number, searchTerm?: string) => {
    return apiService.get<Clients[]>(
      `/clients?page=${page}&limit=${limit}&searchTerm=${searchTerm}`
    );
  },

  createClients: (data: Omit<Clients, "id">) =>
    apiService.post<{ data: Clients }>("/clients", data),

  getClientsById: (id: string | number) =>
    apiService.get<Clients>(`/clients/${id}`),

  updateClients: (id: string | number, data: FormData) =>
    apiService.put<Clients>(`/clients/${id}`, data),

  deleteClients: (id: string | number) =>
    apiService.delete<void>(`/clients/${id}`),
};

export default clientService;
