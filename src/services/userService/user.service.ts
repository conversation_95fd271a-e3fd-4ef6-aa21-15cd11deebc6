// src/api/templateService.ts

import { apiService } from "../api";

interface Template {
  id?: number;
  user_name: string;
  template_name: string;
  template_format: string;
  status?: string;
  created_at?: string;
  data?: any; // For the JSON structure
}

const usersService = {
  getUsers: (page?: number, limit?: number, searchTerm?: string) => {
    return apiService.get<Template[]>(
      `/users?page=${page}&limit=${limit}&searchTerm=${searchTerm}`
    );
  },

  getUsersById: (id: string | number) =>
    apiService.get<Template>(`/users/${id}`),

  createUsers: (data: any) => apiService.post<Template>("/users", data),

  updateUsers: (id: string | number, data: any) =>
    apiService.patch<Template>(`/users/${id}`, data),

  deleteUsers: (id: string | number) => apiService.delete<void>(`/users/${id}`),
};

export default usersService;
