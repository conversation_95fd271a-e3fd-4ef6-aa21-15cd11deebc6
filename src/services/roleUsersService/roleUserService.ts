// services/documentService.ts
import { apiService } from "../api";

interface RoleUsers {}

const roleUserService = {
  getRoleUsers: (page?: number, limit?: number, searchTerm?: string) => {
    return apiService.get<RoleUsers[]>(
      `/users?page=${page}&limit=${limit}&searchTerm=${searchTerm}`
    );
  },

  createRoleUsers: (data: Omit<RoleUsers, "id">) =>
    apiService.post<{ data: RoleUsers }>("/users", data),

  getRoleUsersById: (id: string | number) =>
    apiService.get<RoleUsers>(`/users/${id}`),

  updateRoleUsers: (id: string | number, data: FormData) =>
    apiService.put<RoleUsers>(`/users/${id}`, data),

  deleteRoleUsers: (id: string | number) =>
    apiService.delete<RoleUsers>(`/users/${id}`),
};

export default roleUserService;
