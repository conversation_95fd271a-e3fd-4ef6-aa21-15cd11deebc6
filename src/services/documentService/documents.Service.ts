// services/documentService.ts
import { apiService } from "../api";

interface Document {
  id?: number;
  fileName: string;
  userName: string;
  fileType: string;
  processType: string;
  clientName: string;
  description: string;
  created_at?: string;
  image?: File[];
}

const documentService = {
  getDocuments: (page?: number, limit?: number, searchTerm?: string) => {
    return apiService.get<Document[]>(
      `/documents?page=${page}&limit=${limit}&searchTerm=${searchTerm}`
    );
  },

  getDocumentById: (id: string | number) =>
    apiService.get<Document>(`/documents/${id}`),

  uploadDocument: (data: FormData) =>
    apiService.postFormData<Document>("/documents/upload", data),

  updateDocument: (id: string | number, data: FormData) =>
    apiService.putFormData<Document>(`/documents/file/${id}`, data),

  deleteDocument: (id: string | number) =>
    apiService.delete<void>(`/documents/${id}`),
};

export default documentService;
