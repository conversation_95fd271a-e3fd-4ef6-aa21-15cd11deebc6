import { apiService } from "../api";

interface Template {
  id?: number;
  name: string;
  config: Record<string, {
    contains: string[];
    match_value?: string;
    extract_fields: string[];
  }>;
  created_at?: string;
  updated_at?: string;
}

const templateService = {
  getTemplates: (page?: number, limit?: number, searchTerm?: string) => {
    
    return apiService.get<{ data: Template[] }>(`/templates?page=${page}&limit=${limit}&searchTerm=${searchTerm}`);
  },

  getTemplateById: (id: string | number) =>
    apiService.get<{ data: Template }>(`/templates/${id}`),

  createTemplate: (data: Omit<Template, 'id'>) => 
    apiService.post<{ data: Template }>("/templates", data),

  updateTemplate: (id: string | number, data: Partial<Template>) =>
    apiService.patch<{ data: Template }>(`/templates/${id}`, data),

  deleteTemplate: (id: string | number) =>
    apiService.delete<void>(`/templates/${id}`),
};

export default templateService;