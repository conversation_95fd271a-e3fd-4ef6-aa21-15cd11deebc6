import { apiService } from "@/services/api";
import { ExtractedDataProps } from "./types";

const extracteDataService = {
  getExtracteData: (
    page?: number,
    limit?: number,
    searchTerm?: string,
    clientId?: number,
    jobTypeId?: number,
    startDate?: any
  ) => {
    return apiService.get<{ data: ExtractedDataProps[] }>(
      `/extracted-data?page=${page}&limit=${limit}&searchTerm=${searchTerm}&clientId=${clientId}&startDate=${startDate}&jobTypeId=${jobTypeId}`
    );
  },

  getExtracteDataById: (id: string | number) =>
    apiService.get<{ data: ExtractedDataProps }>(`/extracted-data/${id}`),

  updateExtractedData: (id: string | number, data: any) =>
    apiService.patch<{ data: ExtractedDataProps }>(`/extracted-data/${id}`, {
      data,
    }),
};

export default extracteDataService;
