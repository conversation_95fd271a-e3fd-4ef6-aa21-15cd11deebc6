import { GridColDef, GridPaginationMeta, GridSortModel } from "@mui/x-data-grid";
import { ChangeEvent } from "react";

export type UIDataTableProps = {
  tableName: string;
  columns: GridColDef[];
  handleSearchInputChange?: (query: string) => void;
  searchText?: string;
  tableDataProps: TableDataProps;
  ToolbarItems?: any;
  handlePageChange?: (page: number) => void;
  handlePageSizeChange?: (pageSize: number) => void;
  showPager?: boolean;
  refetch?: any;
};

export type TableDataProps = {
  tableData: RowsProps[];
  totalPage: number;
  page: number;
  pageSize: number;
  handleSortModelChange?: (sortModel: GridSortModel) => void;  
};
export interface RowsProps {
  [key: string]: number | string;
}

export interface OverviewTableProps {
  searchText: string;
  columns: GridColDef[];
  tableDataProps: TableDataProps;
  handlePageChange: (page: number) => void;
  handlePageSizeChange: (pageSize: number) => void;
  showPager?: boolean;
}

export interface RowAction {
  actions: Actions[];
  params?: RowsProps;
}

export type Actions = {
  id: number;
  icon: string;
  title: string;
  onItemClick: (data?: any) => void;
};
