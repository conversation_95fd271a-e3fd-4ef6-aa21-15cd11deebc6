import { memo } from "react";
import Box from "@mui/material/Box";
import Stack from "@mui/material/Stack";
import Paper from "@mui/material/Paper";
import DataTable from "./DataTable";
import { UIDataTableProps } from "./types";
import DebouncedSearch from "../debounced-search/debouncedSearch";

const noop = () => {};

const UIDataTable = memo((props: UIDataTableProps) => {
  const {
    columns,
    handleSearchInputChange = noop,
    searchText = "",
    tableDataProps,
    ToolbarItems,
    handlePageChange = noop,
    handlePageSizeChange = noop,
    showPager = true,
  } = props;

  return (
    <Box component={Paper} >
      <Stack
        direction={{ xs: "column", sm: "row"  }}
       
        alignItems="center"
        sx={{display:"grid", gridTemplateColumns: { xs: "repeat(3, auto)", sm: "repeat(5, auto)" }, columnGap:"10px", justifyContent: ToolbarItems > 1 ? "space-between" : "flex-end"}}
        spacing={2}
      >
        <DebouncedSearch
          id="search-input"
          placeholder="Search for something..."
          delay={500}
          value={searchText}
          onSearch={handleSearchInputChange}
        />
        {/* <TextField
          variant="filled"
          size="small"
          placeholder="Search here"
          value={searchText}
          onChange={handleSearchInputChange}
          sx={{
            mx: { xs: "auto", sm: "initial" },
            maxWidth: { xs: 300, sm: 300 },
          }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconifyIcon icon="eva:search-fill" />
              </InputAdornment>
            ),
          }}
        /> */}
        {/* <Stack
          spacing={{ xs: 2, sm: 2 }}
          direction={{ xs: "column", sm: "row" }}
          justifyContent="flex-end"
          alignItems="center"
          sx={{
            flex: { xs: "1 1 auto", sm: "7" }, // 70% width on small screens
          }}
        > */}



        
        {ToolbarItems}
        
       
        {/* </Stack> */}
      </Stack>
      <Box mt={{ xs: 1.25, sm: 1 }}>
        <DataTable
          searchText={searchText}
          columns={columns}
          tableDataProps={tableDataProps}
          showPager={showPager}
          handlePageChange={handlePageChange}
          handlePageSizeChange={handlePageSizeChange}
        />
      </Box>
    </Box>
  );
});

export default UIDataTable;
