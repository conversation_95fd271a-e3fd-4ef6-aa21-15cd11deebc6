import { DataGrid, useGridApiRef, Grid<PERSON><PERSON> } from "@mui/x-data-grid";
import DataGridFooter from "@/core-components/common/DataGridFooter";
import { OverviewTableProps } from "./types";

const DataTable = ({
  columns,
  tableDataProps,
  handlePageChange,
  handlePageSizeChange,
  showPager,
}: OverviewTableProps) => {
  const { tableData, totalPage, page, pageSize } = tableDataProps;
  const apiRef = useGridApiRef<GridApi>();
  const dataArray = Array.isArray(tableData) ? tableData : [];

  return (
    <DataGrid
      apiRef={apiRef}
      density="standard"
      columns={columns}
      rows={dataArray}
      rowHeight={52}
      getRowId={(row) => row.id}
      sortingMode="server"
      initialState={{
        pagination: { paginationModel: { pageSize: pageSize } },
      }}
      slots={{
        pagination: (props) => (
          <DataGridFooter
            {...props}
            totalPage={totalPage}
            currentPage={page}
            showPager={showPager}
            handlePageChange={handlePageChange}
            handlePageSizeChange={handlePageSizeChange}
            pageSize={pageSize}
          />
        ),
      }}
      checkboxSelection={false}
      pageSizeOptions={[4, 10, 20]}
    />
  );
};
export default DataTable;
