"use client";

import { Controller } from "react-hook-form";
import <PERSON>box<PERSON>ield from "../input-variants/Checkbox";
import { CheckboxInputProps } from "./types";


const CheckboxInput = (props: CheckboxInputProps) => {
  const { id, label, name, control, helperText } = props;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value } }) => (
        <CheckboxField
          label={label}
          id={id}
          checked={value}
          onChange={onChange}
          helperText={helperText}
        />
      )}
    />
  );
};

export default CheckboxInput;
