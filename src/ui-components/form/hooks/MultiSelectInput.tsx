import { Controller } from "react-hook-form";
import { SelectInputProps } from "./types";
import SelectMultipleFiled from "../input-variants/MultiSelect";

const MultiSelectInput = (props: SelectInputProps) => {
  const { id, label, name, control, helperText, options } = props;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value, ref } }) => (
        <SelectMultipleFiled
          {...ref}
          label={label}
          id={id ? id : name}
          fullWidth
          onChange={(e) => {
            const selectedValues = e.target.value as string[];
            onChange(selectedValues);
          }}
          value={Array.isArray(value) ? value : []}
          options={options}
          helperText={helperText as string}
        />
      )}
    />
  );
};

export default MultiSelectInput;
