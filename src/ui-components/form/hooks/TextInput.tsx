"use client";

import { Controller } from "react-hook-form";
import TextFiled from "../input-variants/Text";
import { TextInputProps } from "./types";

const TextInput = (props: TextInputProps) => {
  const {
    id,
    label,
    name,
    control,
    helperText,
    type,
    startAdornment,
    endAdornment,
    fullWidth
  } = props;
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value, ref } }) => (
        <TextFiled
          {...ref}
          label={label}
          id={id ? id : name}
          fullWidth={fullWidth}
          onChange={onChange}
          type={type}
          value={value}
          startAdornment={startAdornment}
          endAdornment={endAdornment}
          helperText={helperText as string}
        />
      )}
    />
  );
};

export default TextInput;
