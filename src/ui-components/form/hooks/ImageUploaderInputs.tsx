"use client";

import { Controller } from "react-hook-form";
import ImageUploader from "../input-variants/ImageUploader";

interface ImageUploaderInputProps {
  name: string;
  label: string;
  helperText?: string;
  control: any;
  onChange: (file: any) => void;
  initialImage?: string | null;
}

const ImageUploaderInput = ({ name, label, control, helperText, onChange, initialImage }: ImageUploaderInputProps) => {

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <ImageUploader
          name={field.name}
          label={label}
          helperText={helperText}
          onFileSelect={(file) => {
            field.onChange(file);
            onChange(file);
          }}
          initialImage={initialImage}
        />
      )}
    />
  );
};

export default ImageUploaderInput;
