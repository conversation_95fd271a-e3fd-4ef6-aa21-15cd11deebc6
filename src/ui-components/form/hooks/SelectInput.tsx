"use client";

import { Controller } from "react-hook-form";
import { SelectInputProps } from "./types";
import SelectFiled from "../input-variants/Select";

export const SelectInput = (props: SelectInputProps) => {
  const {
    id,
    label,
    name,
    control,
    helperText,
    options
  } = props;
  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value, ref } }) => (
        <SelectFiled
          {...ref}
          label={label}
          id={id ? id : name}
          fullWidth
          onChange={onChange}
          value={value}
          options={options}
          helperText={helperText as string}
        />
      )}
    />
  );
};

export default SelectInput;
