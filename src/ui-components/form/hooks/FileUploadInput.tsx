"use client";
import React, { useEffect, useState } from "react";
import { Controller } from "react-hook-form";
import {
  Box,
  Typography,
  Stack,
  Paper,
  IconButton,
  styled,
} from "@mui/material";
import CancelIcon from "@mui/icons-material/Cancel";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import DescriptionIcon from "@mui/icons-material/Description";
import ImageIcon from "@mui/icons-material/Image";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import FolderZipIcon from "@mui/icons-material/FolderZip";

const UploadBox = styled(Box)({
  border: "2px dashed #ccc",
  borderRadius: "8px",
  padding: "16px",
  textAlign: "center",
  cursor: "pointer",
  "&:hover": { borderColor: "#2196F3" },
});

interface FileUploaderInputProps {
  name: string;
  control: any;
  label: string;
  accept?: string;
  helperText?: string;
  onChange?: (files: File[]) => void;
  existingFiles?: (string | File)[];
}

const FileUploaderInput: React.FC<FileUploaderInputProps> = ({
  name,
  control,
  label,
  accept = ".pdf,.png,.docx,.doc,.jpg,.jpeg,.zip", // Added .zip to accepted files
  helperText,
  onChange,
  existingFiles = [],
}) => {
  const [files, setFiles] = useState<File[]>([]);

  useEffect(() => {
    if (existingFiles.length > 0) {
      const existingFileObjects = existingFiles.filter(
        (file) => file instanceof File
      ) as File[];
      setFiles(existingFileObjects);
    }
  }, [existingFiles]);

  const getFileIcon = (file: File) => {
    if (file.type === "application/pdf") {
      return <PictureAsPdfIcon sx={{ fontSize: 40, color: "#FF0000" }} />;
    }
    if (file.type.startsWith("image/")) {
      return <ImageIcon sx={{ fontSize: 40, color: "#4CAF50" }} />;
    }
    if (file.type.includes("word")) {
      return <DescriptionIcon sx={{ fontSize: 40, color: "#2196F3" }} />;
    }
    if (file.type === "application/zip" || file.name.endsWith('.zip')) {
      return <FolderZipIcon sx={{ fontSize: 40, color: "#FF9800" }} />;
    }
    return <InsertDriveFileIcon sx={{ fontSize: 40, color: "#9E9E9E" }} />;
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange: onFieldChange } }) => (
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            {label}
          </Typography>
          <Box component="label" htmlFor="file-upload">
            <UploadBox
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
              }}
            >
              <CloudUploadIcon sx={{ fontSize: 50, color: "#2196F3" }} />
              <Typography variant="body1">Click or Drag to Upload</Typography>
              <Typography variant="caption" color="textSecondary">
                Supported formats: PDF, PNG, JPG, JPEG, DOC, DOCX, ZIP
              </Typography>
            </UploadBox>
          </Box>
          <input
            type="file"
            accept={accept}
            id="file-upload"
            multiple
            style={{ display: "none" }}
            onChange={(e) => {
              if (e.target.files?.length) {
                const newFiles = Array.from(e.target.files);
                setFiles((prevFiles) => {
                  const updatedFiles = [...prevFiles, ...newFiles];
                  onFieldChange(updatedFiles);
                  onChange?.(updatedFiles);
                  return updatedFiles;
                });
              }
            }}
          />

          {helperText && (
            <Typography variant="caption" color="error">
              {helperText}
            </Typography>
          )}

          <Stack spacing={2} mt={2} direction={"column"}>
            {files.map((file, index) => (
              <Paper
                key={index}
                elevation={3}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "8px",
                  borderRadius: "8px",
                  direction: "column",
                }}
              >
                <Box display="flex" alignItems="center" gap={2}>
                  {getFileIcon(file)}
                  <Typography variant="body1">{file.name}</Typography>
                </Box>
                <IconButton
                  onClick={() => {
                    setFiles((prevFiles) => {
                      const updatedFiles = prevFiles.filter(
                        (_, i) => i !== index
                      );
                      onFieldChange(updatedFiles);
                      onChange?.(updatedFiles);
                      return updatedFiles;
                    });
                  }}
                  sx={{ color: "#FF0000" }}
                >
                  <CancelIcon />
                </IconButton>
              </Paper>
            ))}
          </Stack>
        </Box>
      )}
    />
  );
};

export default FileUploaderInput;