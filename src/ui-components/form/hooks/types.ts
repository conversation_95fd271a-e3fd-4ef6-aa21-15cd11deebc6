import { ChangeEvent } from "react";

export type TextInputProps = {
  label: string;
  id?: string;
  name: string;
  control: any;
  type?: string;
  helperText?: string | React.ReactNode;
  startAdornment?: string | React.ReactNode;
  endAdornment?: string | React.ReactNode;
  fullWidth?: boolean;
};

export type SelectInputProps = {
  label: string;
  id?: string;
  name: string;
  control: any;
  helperText?: string | React.ReactNode;
  options: any;
};

export interface CheckboxFieldProps {
  id?: string;
  label: string;
  checked: any;
  onChange: (value: ChangeEvent<HTMLInputElement>) => void;
  helperText?: string;
  fullWidth?: boolean;
  width?: number;
}

export interface CheckboxInputProps {
  id?: string;
  label: string;
  name: string;
  control: any;
  helperText?: string;
}

export type MultiSelectInputProps = {
  label: string;
  id?: string;
  name: string;
  control: any;
  helperText?: string | React.ReactNode;
  options: any;
  onChange?: (value: string[] | string) => void;
};

export type TextAreaInputProps = {
  label: string;
  id?: string;
  rows: number;
  name: string;
  control: any;
  type?: string;
  helperText?: string | React.ReactNode;
  startAdornment?: string | React.ReactNode;
  endAdornment?: string | React.ReactNode;
  fullWidth?: boolean;
};
