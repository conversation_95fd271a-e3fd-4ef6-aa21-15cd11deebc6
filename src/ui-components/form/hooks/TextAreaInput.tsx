"use client";

import { Controller } from "react-hook-form";
import TextAreaFiled from "../input-variants/TextArea";
import { TextAreaInputProps } from "./types";

const TextAreaInput = (props: TextAreaInputProps) => {
  const {
    id,
    label,
    name,
    control,
    helperText,
    rows = 4,
    fullWidth,
    startAdornment,
    endAdornment,
  } = props;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field: { onChange, value, ref } }) => (
        <TextAreaFiled
          {...ref}
          label={label}
          id={id ? id : name}
          fullWidth={fullWidth}
          rows={rows}
          onChange={onChange}
          value={value}
          startAdornment={startAdornment}
          endAdornment={endAdornment}
          helperText={helperText as string}
        />
      )}
    />
  );
};

export default TextAreaInput;
