import OutlinedInput from "@mui/material/OutlinedInput";
import { styled } from "@mui/material/styles";
import Select from "@mui/material/Select";

export const InputStyled = styled(OutlinedInput)(({ theme }) => ({
    border: `1px solid ${theme.palette.grey[300]}`,
    borderRadius: '12px',
    '& fieldset': {
        border: 'none'
    },
    input: {
        padding: '5px 14px'
    }
}));

export const SelectInputStyled = styled(Select)(({ theme }) => ({
    border: `1px solid ${theme.palette.grey[300]}`,
    borderRadius: '12px',
    padding: '10px 16px',
}));