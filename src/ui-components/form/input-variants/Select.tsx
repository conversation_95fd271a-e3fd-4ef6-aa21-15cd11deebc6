import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import { SelectInputStyled } from "../Form.styled";
import { SelectFiledProps } from "./types";
import MenuItem from "@mui/material/MenuItem";

const SelectFiled = ({
  label,
  value,
  onChange,
  helperText,
  fullWidth,
  options,
  width = 100,
}: SelectFiledProps) => {
  const renderHelperText = () => {
    if (helperText) {
      return (
        <FormHelperText error id="outlined-weight-helper-text">
          {helperText}
        </FormHelperText>
      );
    }
  };
  return (
    <FormControl
      sx={{ width: `${fullWidth ? 100 : width}%` }}
      variant="outlined"
    >
      <label>{label}</label>
      <SelectInputStyled
        id="demo-simple-select-helper"
        value={value}
        onChange={onChange}
        displayEmpty
        margin="dense"
        inputProps={{ "aria-label": "Without label" }}
      >
        {options?.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </SelectInputStyled>
      {renderHelperText()}
    </FormControl>
  );
};

export default SelectFiled;
