import React, { useRef, useState } from "react";
import { Typography, Box, FormHelperText } from "@mui/material";
import Image from "next/image";
import { ImageUploaderProps } from "./types";

const ImageUploader = ({
  label,
  helperText,
  onFileSelect,
  initialImage,
}: ImageUploaderProps) => {
  const [imagePreview, setImagePreview] = useState<string | null>(initialImage ?? null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleImageChange = (file: File | null) => {
    if (!file) {
      setImagePreview(null);
      onFileSelect(null); // Ensure passing null to indicate no file
      return;
    } else {
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
        onFileSelect(file);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleImageChange(file);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleImageChange(file);
    }
  };

  const handleRemove = () => {
    setImagePreview(null);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onFileSelect(null);
  };


  return (
    <Box>
      <Typography variant="body2" sx={{ mb: 1 }}>
        {label}
      </Typography>
      <Box
        onClick={() => fileInputRef.current?.click()}
        onDragOver={(e) => e.preventDefault()}
        onDragEnter={(e) => e.preventDefault()}
        onDrop={handleDrop}
        sx={{
          border: "2px dashed #aaa",
          padding: "20px",
          textAlign: "center",
          borderRadius: "8px",
          width: "100%",
          height: "250px",
          cursor: "pointer",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          transition: "border-color 0.3s ease",
          "&:hover": {
            borderColor: "#555",
          },
        }}
      >
        {imagePreview ? (
          <img
            src={imagePreview}
            alt="Preview"
            style={{
              width: "100%",
              height: "100%",
              objectFit: "contain",
              borderRadius: "8px",
            }}
          />
        ) : (
          <Box>
            <Image
              src="/image/Upload.svg"
              alt="Upload Icon"
              width={60}
              height={60}
              style={{ marginBottom: "10px" }}
            />
            <Typography variant="body2" sx={{ color: "#555" }}>
              Drop files here or click to upload
            </Typography>
          </Box>
        )}
        <input
          ref={fileInputRef}
          type="file"
          style={{ display: "none" }}
          onChange={handleFileInputChange}
        />
      </Box>
      {imagePreview && (
        <Box onClick={handleRemove}>
          <Typography variant="h6" sx={{ cursor: "pointer", fontSize: "14px" }}>
            Remove File
          </Typography>
        </Box>
      )}
      {helperText && (
        <FormHelperText error id="outlined-weight-helper-text">
          {helperText}
        </FormHelperText>
      )}
    </Box>
  );
};

export default ImageUploader;
