import FormControl from "@mui/material/FormControl";
import InputAdornment from "@mui/material/InputAdornment";
import FormHelperText from "@mui/material/FormHelperText";
import { InputStyled } from "../Form.styled";
import { TextFiledProps } from "./types";

const TextFiled = ({
  label,
  value,
  onChange,
  type,
  startAdornment,
  endAdornment,
  id = "outlined-adornment-input",
  ariaLabel = "input",
  helperText,
  fullWidth,
  width = 100,
}: TextFiledProps) => {
  const renderHelperText = () => {
    if (helperText) {
      return (
        <FormHelperText error id="outlined-weight-helper-text">
          {helperText}
        </FormHelperText>
      );
    }
  };
  return (
    <FormControl
      sx={{ width: `${fullWidth ? 100 : width}%` }}
      variant="outlined"
    >
      <label>{label}</label>
      <InputStyled
        id={id}
        startAdornment={
          startAdornment && (
            <InputAdornment position="start">{startAdornment}</InputAdornment>
          )
        }
        endAdornment={
          endAdornment && (
            <InputAdornment position="end">{endAdornment}</InputAdornment>
          )
        }
        aria-describedby={`${id}-helper-text`}
        inputProps={{
          "aria-label": ariaLabel,
        }}
        margin="dense"
        onChange={onChange}
        value={value}
        type={type} 
      />
      {renderHelperText()}
    </FormControl>
  );
};

export default TextFiled;
