import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import { SelectInputStyled } from "../Form.styled";
import { MultiSelectField } from "./types";
import MenuItem from "@mui/material/MenuItem";
import Checkbox from "@mui/material/Checkbox";
import ListItemText from "@mui/material/ListItemText";
import { useMemo } from "react";

const SelectMultipleField = ({
  label,
  value = [],
  onChange,
  helperText,
  fullWidth,
  options,
  width = 100,
}: MultiSelectField) => {
  const safeValue = useMemo(() => (Array.isArray(value) ? value : []), [value]);
  const allValues = useMemo(() => options?.map((opt) => opt.value) || [], [options]);

  const isAllSelected = safeValue.length === allValues.length;

  const handleChange = (event: any) => {
    const selected = event.target.value;

    if (selected.includes("select_all")) {
      const newValue = isAllSelected ? [] : allValues;
      onChange?.({ target: { value: newValue } } as any);
    } else {
      onChange?.(event);
    }
  };

  const renderHelperText = useMemo(() => {
    if (helperText) {
      return (
        <FormHelperText error id="outlined-weight-helper-text">
          {helperText}
        </FormHelperText>
      );
    }
    return null;
  }, [helperText]);

  return (
    <FormControl sx={{ width: `${fullWidth ? 100 : width}%` }} variant="outlined">
      <label>{label}</label>
      <SelectInputStyled
        multiple
        displayEmpty
        margin="dense"
        value={safeValue}
        onChange={handleChange}
        renderValue={(selected: any) => {
          if (selected.length === 0) return <p>Select options</p>;
          const selectedLabels = options
            ?.filter((opt) => selected.includes(opt.value))
            .map((opt) => opt.label)
            .join(", ");
          return selectedLabels;
        }}
        inputProps={{ "aria-label": "Without label" }}
      >
        <MenuItem value="select_all">
          <Checkbox
            checked={isAllSelected}
            indeterminate={safeValue.length > 0 && !isAllSelected}
          />
          <ListItemText
            primary={isAllSelected ? "Unselect All" : "Select All"}
            primaryTypographyProps={{ style: { marginLeft: "20px", fontWeight: 500 } }}
          />
        </MenuItem>

        {options?.map((option: any) => (
          <MenuItem key={option.value} value={option.value}>
            <Checkbox checked={safeValue.includes(option.value)} />
            <ListItemText
              primary={option.label}
              primaryTypographyProps={{ style: { marginLeft: "20px", color: "#2B3674" } }}
            />
          </MenuItem>
        ))}
      </SelectInputStyled>
      {renderHelperText}
    </FormControl>
  );
};

export default SelectMultipleField;
