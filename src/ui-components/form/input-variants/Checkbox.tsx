import FormControl from "@mui/material/FormControl";
import FormControlLabel from "@mui/material/FormControlLabel";
import Checkbox from "@mui/material/Checkbox";
import FormHelperText from "@mui/material/FormHelperText";
import { CheckboxFieldProps } from "../hooks/types";


const CheckboxField = ({
  label,
  checked,
  onChange,
  helperText,
  fullWidth = true,
  width = 100,
}: CheckboxFieldProps) => {
  const renderHelperText = () => {
    if (helperText) {
      return (
        <FormHelperText error>
          {helperText}
        </FormHelperText>
      );
    }
  };

  return (
    <FormControl sx={{ width: `${fullWidth ? 100 : width}%` }}>
      <FormControlLabel
        control={
          <Checkbox
            checked={checked}
            onChange={onChange}
            color="primary"
          />
        }
        label={label}
      />
      {renderHelperText()}
    </FormControl>
  );
};

export default CheckboxField;
