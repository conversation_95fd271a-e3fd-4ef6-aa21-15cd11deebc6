import { SelectChangeEvent } from "@mui/material/Select";
import { InputHTMLAttributes } from "react";

export interface TextFiledProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  value?: string;
  fullWidth?: boolean;
  width?: number | string;
  onChange?: () => void;
  startAdornment?: string | React.ReactNode;
  endAdornment?: string | React.ReactNode;
  id?: string;
  ariaLabel?: string;
  helperText?: string;
}

export interface SelectFiledProps extends TextFiledProps {
  options: { value: string | number; label: string }[];
}

export interface ImageUploaderProps {
  name: string;
  label: string;
  helperText?: string;
  onFileSelect: (file: File | null) => void;
  initialImage:string | null | undefined;
}

export interface MultiSelectField {
  id?: string;
  label: string;
  value: string[];
  onChange: (event: SelectChangeEvent<unknown>) => void;
  helperText?: string;
  fullWidth?: boolean;
  options: { value: string | number; label: string }[];
  width?: number;
}
