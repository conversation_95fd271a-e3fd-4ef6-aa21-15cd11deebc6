import FormControl from "@mui/material/FormControl";
import InputAdornment from "@mui/material/InputAdornment";
import FormHelperText from "@mui/material/FormHelperText";
import TextField from "@mui/material/TextField";
import { TextFiledProps } from "./types";

const TextAreaFiled = ({
  label,
  value,
  onChange,
  startAdornment,
  endAdornment,
  id = "outlined-adornment-input",
  ariaLabel = "textarea",
  helperText,
  fullWidth,
  width = 100,
  rows = 4,
}: TextFiledProps & { rows?: number }) => {
  const renderHelperText = () => {
    if (helperText) {
      return (
        <FormHelperText error id={`${id}-helper-text`}>
          {helperText}
        </FormHelperText>
      );
    }
  };

  return (
    <FormControl
      sx={{ width: `${fullWidth ? 100 : width}%` }}
      variant="outlined"
    >
      <label htmlFor={id}>{label}</label>
      <TextField
        id={id}
        InputProps={{
          startAdornment: startAdornment && (
            <InputAdornment position="start">{startAdornment}</InputAdornment>
          ),
          endAdornment: endAdornment && (
            <InputAdornment position="end">{endAdornment}</InputAdornment>
          ),
        }}
        aria-describedby={`${id}-helper-text`}
        inputProps={{
          "aria-label": ariaLabel,
        }}
        margin="dense"
        onChange={onChange}
        value={value}
        multiline
        rows={rows}
      />
      {renderHelperText()}
    </FormControl>
  );
};

export default TextAreaFiled;
