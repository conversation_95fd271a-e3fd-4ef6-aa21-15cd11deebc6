import * as React from "react";
import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";

type AutocompleteProps = {
  id: string;
  width?: number;
  value: any;
  options: OptionsType[];
  onChange: (event: React.SyntheticEvent, value: OptionsType | null) => void;
};

interface OptionsType {
  value: string | number;
  label: string;
}

const VrAutomcomplete = ({
  id,
  width = 300,
  options = [],
  value,
  onChange,
}: AutocompleteProps) => {
  return (
    <Autocomplete
      id={id}
      sx={{ width, padding: 0 }}
      disableClearable
      options={options}
      autoHighlight
      isOptionEqualToValue={(option, val) => option.value === val?.value}
      value={value}
      onChange={onChange}
      getOptionLabel={(option) => option.label || ""}
      renderOption={(props, option) => {
        const { key, ...optionProps } = props;
        return (
          <Box
            key={key}
            component="li"
            sx={{ "& > img": { mr: 2, flexShrink: 0 } }}
            {...optionProps}
          >
            {option.label}
          </Box>
        );
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          margin="dense"
          inputProps={{
            ...params.inputProps,
            autoComplete: "new-password",
          }}
        />
      )}
    />
  );
};

export default VrAutomcomplete;
