import React, { useState } from "react";
import { ImageStyled } from "./Image.styled";

interface CustomImageProps {
  imageUrl: string;
  alt: string;
}

const CustomImage: React.FC<CustomImageProps> = ({ imageUrl, alt }) => {
  const [currentSrc, setCurrentSrc] = useState(imageUrl);

  const handleImageError = () => {
    setCurrentSrc("/images/Product-inside.webp"); // Switch to the fallback image
  };

  return (
    <ImageStyled
      src={currentSrc}
      alt={alt}
      layout="intrinsic"
      quality={100}
      height={100}
      width={40}
      onError={handleImageError}
    />
  );
};

export default CustomImage;
