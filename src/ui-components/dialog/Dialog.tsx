import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useTheme } from "@mui/material/styles";
import { DialoProps } from "./types";
import IconButton from "@mui/material/IconButton";
import { memo } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";


const AppDialog = memo((props: DialoProps) => {
  const { open, handleClose, title, body, actions, maxWidth = "md" } = props;
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down("md"));
  return (
    <Dialog
      fullScreen={fullScreen}
      fullWidth={true}
      open={open}
      keepMounted
      onClose={handleClose}
      maxWidth={maxWidth}
      aria-labelledby="responsive-dialog-title"
      scroll="paper"
    >
      {title ? (
        <DialogTitle id="responsive-dialog-title">{title}</DialogTitle>
      ) : null}
      <IconButton
        aria-label="close"
        onClick={handleClose}
        sx={(theme) => ({
          position: "absolute",
          right: 8,
          top: 8,
          background: theme.palette.action.active,
          color: theme.palette.common.white,
        })}
      >
         <Icon icon="material-symbols:close" />
      </IconButton>
      <DialogContent>{body}</DialogContent>
      {actions ? <DialogActions>{actions}</DialogActions> : null}
    </Dialog>
  );
});

export default AppDialog;
