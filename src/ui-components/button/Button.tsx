import React from 'react';
import { Button, CircularProgress } from '@mui/material';
import { Icon } from '@iconify/react';
import { AppButtonProps } from './types';

const AppButton: React.FC<AppButtonProps> = ({ icon, loading, children, ...rest }) => {
  const isSaveButton = typeof children === 'string' && children.toLowerCase() === 'save';
  const variant = isSaveButton ? 'contained' : 'outlined';
  return (
    <Button
      variant={variant}
      startIcon={!loading && icon ? <Icon icon={icon} /> : null}
      {...rest}
      sx={{
        textTransform: 'none',
        borderRadius: 8,
        padding: '8px 16px',
        ...(icon && { paddingLeft: '12px' }),
        float: "right",
        mt:"30px",
        ...(variant === "outlined" && {
            color:"#000",
            borderColor:"black",
            mr:"10px"
        })
      }}
      disabled={loading || rest.disabled}
    >
      {loading ? <CircularProgress size={20} color="inherit" /> : children}
    </Button>
  );
};

export default AppButton;
