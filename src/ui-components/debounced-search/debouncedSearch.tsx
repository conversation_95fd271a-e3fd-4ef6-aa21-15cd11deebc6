import React, { useState, useEffect } from "react";
import TextField from "@mui/material/TextField";
import IconifyIcon from "@/core-components/base/IconifyIcon";
import InputAdornment from "@mui/material/InputAdornment";

type SearchInputProps = {
  id: string;
  width?: number | string;
  placeholder?: string;
  value: string;
  delay?: number;
  onSearch: (value: string) => void;
};

const DebouncedSearch = (props: SearchInputProps) => {
  const {
    id,
    width = 300,
    placeholder = "Type to search...",
    value = "",
    delay = 300,
    onSearch,
  } = props;

  const [searchTerm, setSearchTerm] = useState(value);
  const [debouncedValue, setDebouncedValue] = useState(value);

  // Update debounced value after delay
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(searchTerm.trim());
    }, delay);

    return () => {
      clearTimeout(handler); // Cleanup timer on value change
    };
  }, [searchTerm, delay]);

  // Call onSearch when debouncedValue changes
  useEffect(() => {
    onSearch(debouncedValue.trim());
  }, [debouncedValue, onSearch]);

  // Handle input change
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  return (
    <TextField
      id={id}
      value={searchTerm.trim()}
      onChange={handleChange}
      placeholder={placeholder}
      variant="outlined"
      sx={{
        width: width,
      }}
      InputProps={{
        endAdornment: (
          <InputAdornment position="end">
            <IconifyIcon icon="eva:search-fill" />
          </InputAdornment>
        ),
      }}
    />
  );
};

export default DebouncedSearch;
