import * as React from "react";
import dayjs from "dayjs";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import InputLabel from "@mui/material/InputLabel";

interface CustomDatePickerProps {
  value: string | null;
  onChange: (newDate: dayjs.Dayjs | null) => void;
  label: string;
}

const CustomDatePicker = ({
  value,
  onChange,
  label,
}: CustomDatePickerProps) => {
  const validValue = value && dayjs(value).isValid() ? dayjs(value) : null;
  return (
    <div>
      <InputLabel sx={{position:"relative",top:"0px"}}>{label}</InputLabel>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker sx={{mb:2}} value={validValue} onChange={onChange} />
      </LocalizationProvider>
    </div>
  );
};

export default CustomDatePicker;
