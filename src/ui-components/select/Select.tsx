import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";

const OptionSelection = (props: any) => {
  const { value, onChange, options,width } = props;
  return (
    <Select
      id="demo-simple-select-helper"
      value={value}
      onChange={(event) => onChange(event?.target?.value)}
      displayEmpty
      margin="dense"
      sx={{
        width: width,
      }}
      inputProps={{ "aria-label": "Without label" }}
    >
      {options?.map((item: any,index:number) => {
        const { value, label } = item;
        return <MenuItem value={value} key={index}>{label}</MenuItem>;
      })}
    </Select>
  );
};

export default OptionSelection;
