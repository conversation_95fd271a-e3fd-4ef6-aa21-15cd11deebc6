import { Icon } from "@iconify/react/dist/iconify.js";
import { Box, Button, Typography } from "@mui/material";
import React, { useState } from "react";

const Refresh = ({ onRefetch }: any) => {
  const [loading, setLoading] = useState(false);

  const handleRefresh = async () => {
    setLoading(true);
    await onRefetch();
    setLoading(false);
  };

  return (
    <>
      <Button
        variant="contained"
        onClick={handleRefresh}
        disabled={loading}
        sx={{height:"30px",borderRadius:"5px"}}
      >
        Refresh <Icon icon="eva:refresh-outline" style={{ marginLeft: "10px" }} />
      </Button>

      {loading && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            zIndex:"1"
          }}
        >
          <Typography
            variant="body1"
            sx={{
              color: "#000",
              backgroundColor: "#fff",
              padding: "10px 20px",
              borderRadius: "5px",
              border:"1px solid grey"
            }}
          >
            Processing...
          </Typography>
        </Box>
      )}
    </>
  );
};

export default Refresh;
