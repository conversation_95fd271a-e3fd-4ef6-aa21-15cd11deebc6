import { Box, BoxProps, Tooltip } from "@mui/material";
import { Icon, IconProps } from "@iconify/react";

interface IconifyProps extends BoxProps {
  icon: IconProps["icon"];
  tooltip?: string;
}

const IconifyIcon = ({ icon, tooltip, ...rest }: IconifyProps) => {
  return (
    <Tooltip title={tooltip || ""} arrow>
      <Box component={Icon as React.ElementType} icon={icon} sx={{ cursor: 'pointer' }} {...rest} />
    </Tooltip>
  );
};

export default IconifyIcon;
