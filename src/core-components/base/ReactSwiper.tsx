import { forwardRef } from 'react';
import { Box, BoxProps } from '@mui/material';
import { Swiper } from 'swiper/react';
import { Swiper as SwiperClass } from 'swiper/types';
import { Navigation, Pagination, Scrollbar, A11y } from 'swiper/modules';

export interface ReactSwiperProps extends BoxProps {
  slidesPerView: number;
  children: React.ReactNode;
  onBeforeInit?: (swiper: SwiperClass) => void;
  onSlideChange?: (swiper: SwiperClass) => void;
}

// Forward the correct SwiperClass ref
const ReactSwiper = forwardRef<SwiperClass | null, ReactSwiperProps>(
  ({ children, slidesPerView, onBeforeInit, onSlideChange, ...rest }, ref) => {
    return (
      <Box width={1} {...rest}>
        <Swiper
          // Forward ref to Swiper instance
          onSwiper={(swiperInstance:any) => {
            if (typeof ref === 'function') {
              ref(swiperInstance);
            } else if (ref) {
              ref.current = swiperInstance;
            }
          }}
          modules={[Navigation, Pagination, Scrollbar, A11y]}
          spaceBetween={20}
          slidesPerView={slidesPerView}
          onBeforeInit={onBeforeInit}
          onSlideChange={onSlideChange}
        >
          {children}
        </Swiper>
      </Box>
    );
  }
);

export default ReactSwiper;
