import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import Pagination from "@mui/material/Pagination";
import OptionSelection from "@/ui-components/select/Select";

const options = [
  {
    label: "10",
    value: 10,
  },
  {
    label: "25",
    value: 25,
  },
  {
    label: "50",
    value: 50,
  },
  {
    label: "100",
    value: 100,
  },
];

export type TableFooterProps = {
  totalPage: number;
  handlePageChange: (value: number) => void;
  handlePageSizeChange: (value: number) => void;
  currentPage: number;
  pageSize: number;
  showPager?: boolean;
};

const DataGridFooter = (props: TableFooterProps) => {
  const {
    totalPage,
    handlePageChange,
    handlePageSizeChange,
    currentPage,
    pageSize,
    showPager,
  } = props;

  const handleNextPage = (
    event: { preventDefault: () => void },
    value: number
  ) => {
    event.preventDefault();
    handlePageChange(value);
  };

  if (!showPager) {
    return null;
  }

  const startRow = (currentPage - 1) * pageSize + 1;
  const endRow = currentPage * pageSize;

  const startPage = totalPage > 0 ? startRow : 0;
  const endPage = totalPage > 0 ? endRow : 0;
  const total = totalPage > 0 ? totalPage : 0;

  return (
    <Stack
      alignItems="center"
      justifyContent="space-between"
      pl={3}
      pr={1.6}
      width={1}
    >
      <OptionSelection
        options={options}
        onChange={handlePageSizeChange}
        value={pageSize}
        width={100}
      />
      <Typography variant="body2" color="text.primary">
        {startPage} to {endPage} of {total}
      </Typography>
      <Pagination
        count={totalPage}
        page={currentPage}
        onChange={handleNextPage}
      />
    </Stack>
  );
};

export default DataGridFooter;
