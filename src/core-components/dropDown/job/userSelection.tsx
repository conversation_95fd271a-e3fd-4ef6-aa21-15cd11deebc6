import VrAutomcomplete from "@/ui-components/autocomplete/Autocomplete";
import { Stack } from "@mui/material";
import { useEffect, useMemo } from "react";

const UserSelection = (props: any) => {
  const { onUserChange, userValue, userData } = props;

  const options = useMemo(() => {
    return (
      userData?.data?.map((user: any) => ({
        value: user.id,
        label: user.name,
      })) || []
    );
  }, [userData]);

  const defaultUser = useMemo(() => {
    return options.find((user: any) => user.value === 16);
  }, [options]);

  useEffect(() => {
    if (defaultUser && (userValue === null || userValue === undefined)) {
      onUserChange(defaultUser);
    }
  }, [defaultUser, userValue, onUserChange]);

  const selectedValue = useMemo(() => {
    if (userValue) {
      return (
        options.find((option: any) => option.value === userValue.value) || null
      );
    }
    return null;
  }, [userValue, options]);

  return (
    <Stack
      sx={{
        mx: { xs: "auto", sm: "initial" },
        minWidth: { xs: 200, sm: 200 },
      }}
    >
      <VrAutomcomplete
        options={options}
        value={selectedValue}
        onChange={(_, newValue) => onUserChange(newValue || null)}
        id={"userId"}
      />
    </Stack>
  );
};

export default UserSelection;
