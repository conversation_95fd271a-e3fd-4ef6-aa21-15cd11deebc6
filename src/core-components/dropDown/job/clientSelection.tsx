import VrAutomcomplete from "@/ui-components/autocomplete/Autocomplete";
import { Stack } from "@mui/material";
import { useEffect, useMemo } from "react";

const ClientSelection = (props: any) => {
  const { onclientChange, clientValue, clientData } = props;

  const options = useMemo(() => {
    return (
      clientData?.data?.map((client: any) => ({
        value: client.id,
        label: client.name,
      })) || []
    );
  }, [clientData]);


  const defaultClient = useMemo(() => {
    return options.find((client: any) => client.value === 16);
  }, [options]);


  useEffect(() => {
    if (defaultClient && (clientValue === null || clientValue === undefined)) {
      onclientChange(defaultClient);
    }
  }, [defaultClient, clientValue, onclientChange]);

  const selectedValue = useMemo(() => {
    if (clientValue) {
      return (
        options.find((option: any) => option.value === clientValue.value) ||
        null
      );
    }
    return null;
  }, [clientValue, options]);

  return (
    <Stack
      sx={{
        mx: { xs: "auto", sm: "initial" },
        minWidth: { xs: 200, sm: 200 },
      }}
    >
      <VrAutomcomplete
        options={options}
        value={selectedValue}
        onChange={(_, newValue) => onclientChange(newValue || null)}
        id={"clientId"}
      />
    </Stack>
  );
};

export default ClientSelection;
