import { jobTypeOption } from "@/config/documentCongif/documentCongif";
import VrAutomcomplete from "@/ui-components/autocomplete/Autocomplete";
import { Stack } from "@mui/material";
import { useEffect, useMemo } from "react";

const JobTypeSelection = (props: any) => {
  const { onJobTypeChange, jobTypeValue } = props;

  const defaultOption = useMemo(() => {
    return jobTypeOption.find((option) => option.value === "SEW");
  }, []);

  useEffect(() => {
    if (
      defaultOption &&
      (jobTypeValue === null || jobTypeValue === undefined)
    ) {
      onJobTypeChange(defaultOption);
    }
  }, [defaultOption, jobTypeValue, onJobTypeChange]);

  const selectedValue = useMemo(() => {
    if (jobTypeValue) {
      return (
        jobTypeOption.find(
          (option: any) => option.value === jobTypeValue.value
        ) || null
      );
    }
    return null;
  }, [jobTypeValue]);

  return (
    <Stack
      sx={{
        mx: { xs: "auto", sm: "initial" },
        minWidth: { xs: 200, sm: 200 },
      }}
    >
      <VrAutomcomplete
        options={jobTypeOption}
        value={selectedValue}
        onChange={(_, newValue) => onJobTypeChange(newValue || null)}
        id="jobType"
      />
    </Stack>
  );
};

export default JobTypeSelection;
