import * as React from "react";
import { styled, alpha } from "@mui/material/styles";
import Button from "@mui/material/Button";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import Menu, { MenuProps } from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import { StyledMenu } from "./ExportDropdown.styled";
import { ExportPdfDownloadProps } from "./type";

const ExportDropdown = ({
  firstHeaders,
  firstTableData = [],
  fileName = "ExportedFile",
}: ExportPdfDownloadProps) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const generatePDF = () => {
   
    handleClose();
  };

  const generateExcel = () => {
    const data = [firstHeaders, ...firstTableData];

    handleClose();
  };

  const generateCSV = () => {
    const csvContent = [
      firstHeaders?.map((header: string) => `"${header}"`).join(","),
      ...firstTableData.map((row: string[]) =>
        row.map((cell: string) => `"${cell}"`).join(",")
      ),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  
    handleClose();
  };

  return (
    <div>
      <Button
        id="demo-customized-button"
        aria-controls={open ? "demo-customized-menu" : undefined}
        aria-haspopup="true"
        aria-expanded={open ? "true" : undefined}
        variant="contained"
        disableElevation
        onClick={handleClick}
        endIcon={<KeyboardArrowDownIcon/>}
        sx={{height:"30px",borderRadius:"5px"}}
      >
        Export
      </Button>
      <StyledMenu
        id="demo-customized-menu"
        MenuListProps={{
          "aria-labelledby": "demo-customized-button",
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
      >
        <MenuItem onClick={generateCSV} disableRipple>
          CSV
        </MenuItem>
        <MenuItem onClick={generateExcel} disableRipple>
          Excel
        </MenuItem>
        <MenuItem onClick={generatePDF} disableRipple>
          PDF
        </MenuItem>
      </StyledMenu>
    </div>
  );
};

export default ExportDropdown;
