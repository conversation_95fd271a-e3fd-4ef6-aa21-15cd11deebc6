import { z } from "zod";

export const DocumentSchema = z.object({
  userId: z.number().optional(),
  clientId: z.number().nullable().optional(),
  description: z.string().optional(),
  file: z
    .array(
      z.union([
        z.string(),
        z
          .instanceof(File)
          .refine(
            (file) =>
              [
                "application/pdf",
                "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "image/png",
                "image/jpeg",
                "application/zip", // Added ZIP support
              ].includes(file.type) || file.name.endsWith('.zip'), // Also check file extension
            {
              message: "Only PDF, DOC, DOCX, PNG, JPG, JPEG, ZIP files are allowed.",
            }
          ),
      ])
    )
    .nullable()
    .default(null),
});