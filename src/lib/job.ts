import { z } from "zod";

export const defaultValuesSchema = z.object({
  id: z.number().optional(),
  user_id: z.union([z.string(), z.number()]),
  client_id: z.union([z.string(), z.number()]),
  job_name: z.string().min(1, "Job name is required"),
  job_type: z.string().min(1, "Job type is required"),
  // template_id: z.union([z.string(), z.number(), z.null()]).optional(),
  document_id: z
    .array(z.number()) // Ensure array of numbers
    .min(1, "At least one document is required"),
  description: z.string().optional(),
  status: z.string(),
});
