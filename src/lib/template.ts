import { z } from "zod";

export const TemplateSchema = z.object({
  name: z.string().min(1, "Template name is required").optional(),
  created_by: z.number().min(1, "Creator ID is required").optional(),
  sections: z.array(
    z.object({
      sectionName: z.string().min(1, "Section name is required"),
      contains: z.string().optional(),
      match_value: z.string().optional(),
      extract_fields: z.array(
        z.object({
          value: z.string().min(1, "Field cannot be empty")
        })
      ).min(1, "At least one extract field is required")
    })
  ).min(1, "At least one section is required")
});
export type TemplateFormType = z.infer<typeof TemplateSchema>;