import { z } from "zod";

export const ClientSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, "Client name is required"),
  email: z
    .string()
    .nonempty({ message: "Email is required" })
    .email("Invalid email format"),
  phone: z.string().regex(/^\d{10}$/, "Phone number must be 10 digits"),
  address: z.string().min(1, "Address is required"),
  status: z.union([z.number(), z.boolean()]),
  description: z.string().min(1, "Description is required"),
});
