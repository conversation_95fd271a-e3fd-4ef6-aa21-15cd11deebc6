'use client'

import { useEffect } from 'react'
import Head from 'next/head'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('Global error caught:', error)
  }, [error])

  return (
    <html>
      <Head>
        <title>Something went wrong!</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <body style={{
        fontFamily: 'sans-serif',
        padding: '2rem',
        maxWidth: '800px',
        margin: '0 auto',
        textAlign: 'center'
      }}>
        <div style={{ marginBottom: '2rem' }}>
          <h1 style={{ fontSize: '2rem', color: '#dc2626' }}>Oops! Something went wrong</h1>
          <p style={{ color: '#6b7280' }}>We're sorry for the inconvenience. Our team has been notified.</p>
          {error.digest && (
            <p style={{ fontSize: '0.875rem', color: '#9ca3af' }}>
              Error reference: {error.digest}
            </p>
          )}
        </div>
        
        <button
          onClick={() => reset()}
          style={{
            backgroundColor: '#2563eb',
            color: 'white',
            padding: '0.75rem 1.5rem',
            borderRadius: '0.375rem',
            border: 'none',
            cursor: 'pointer',
            fontSize: '1rem',
            fontWeight: '600',
            transition: 'background-color 0.2s'
          }}
          onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#1d4ed8'}
          onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#2563eb'}
        >
          Try Again
        </button>
        
        <div style={{ marginTop: '2rem', fontSize: '0.875rem' }}>
          <p>If the problem persists, please contact support.</p>
          <p>
            <a 
              href="/" 
              style={{ color: '#2563eb', textDecoration: 'underline' }}
            >
              Return to homepage
            </a>
          </p>
        </div>
      </body>
    </html>
  )
}