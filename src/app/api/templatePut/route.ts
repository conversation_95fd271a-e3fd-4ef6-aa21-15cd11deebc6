import fs from "fs";
import path from "path";
import { NextResponse } from "next/server";

const filePath = path.join(
  process.cwd(),
  "public",
  "StoreFiles",
  "Template.json"
);

const readJsonFile = () => {
  try {
    if (fs.existsSync(filePath)) {
      return JSON.parse(fs.readFileSync(filePath, "utf-8"));
    }
  } catch (error) {
    console.error("Error reading JSON file:", error);
  }
  return [];
};

const writeJsonFile = (data: any) => {
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), "utf-8");
  } catch (error) {
    console.error("Error writing JSON file:", error);
  }
};

export async function PUT(req: Request) {
  try {
    const body = await req.json();
    const { id } = body;

    if (!id || !body.sections || !Array.isArray(body.sections)) {
      return NextResponse.json(
        { error: "Invalid input data" },
        { status: 400 }
      );
    }

    const jsonData = readJsonFile();

    const index = jsonData.findIndex((entry: any) => entry.id === id);

    if (index === -1) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      );
    }

    const updatedEntry: any = {
      id: id,
      templateName: body.templateName || jsonData[index].templateName,
      userName: body.userName || jsonData[index].userName,
      created_at: jsonData[index].created_at,
      data: {
       
      },
    };
    
    body.sections.forEach((section: any) => {
      const sectionName = section.sectionName;
      const contains = section.contains?.trim();
      const matchValue = section.match_value;
      const extractFields = section.extract_fields
        .map((field: any) => field.value)
        .filter((value: any) => value !== null && value !== "");

      const sectionData: any = {
        type: "section",
        extract_fields: extractFields,
      };

      if (matchValue && matchValue.trim() !== "") {
        sectionData.match_value = matchValue;
      }

      if (contains && contains !== "") {
        sectionData.contains = [contains];
      }

      updatedEntry.data[sectionName] = sectionData;
    });

    jsonData[index] = updatedEntry;
    writeJsonFile(jsonData);

    return NextResponse.json({ message: "Data updated successfully!" });
  } catch (error) {
    console.error("Error updating data:", error);
    return NextResponse.json(
      { message: "Failed to update data." },
      { status: 500 }
    );
  }
}