import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import path from "path";

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    const filePath = path.join(
      process.cwd(),
      "public",
      "StoreFiles",
      "FileDocs.json"
    );

    let oldData = {};
    if (fs.existsSync(filePath)) {
      const previousContent = fs.readFileSync(filePath, "utf-8");
      oldData = JSON.parse(previousContent);
    }

    fs.writeFileSync(filePath, JSON.stringify(body, null, 2));

    return NextResponse.json({ message: "File saved successfully" });
  } catch (error) {
    console.error("Error saving JSON:", error);
    return new NextResponse("Error saving JSON", { status: 500 });
  }
}
