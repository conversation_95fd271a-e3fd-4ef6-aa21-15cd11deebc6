import { NextResponse } from "next/server";
import { promises as fs } from "fs";
import path from "path";
import bcrypt from "bcryptjs";

const filePath = path.join(process.cwd(), "src/app/db/RoleMangement.json");

export async function POST(req: Request) {
  try {
    const clientData = await req.json();
    let clients: any[] = [];

    try {
      const fileData = await fs.readFile(filePath, "utf-8");
      clients = JSON.parse(fileData || "[]");
    } catch {
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      await fs.writeFile(filePath, "[]", "utf-8");
    }

    const hashedPassword = await bcrypt.hash(clientData.password, 10);

    const newClient = { id: clients.length + 1, ...clientData, password: hashedPassword };
    clients.push(newClient);

    await fs.writeFile(filePath, JSON.stringify(clients, null, 2), "utf-8");

    return NextResponse.json(newClient, { status: 201 });
  } catch (error) {
    console.error("Error adding RoleMangement:", error);
    return NextResponse.json(
      { message: "Error adding RoleMangement" },
      { status: 500 }
    );
  }
}
