import { NextResponse } from "next/server";
import fs from "fs";
import path from "path";

const uploadDir = path.join(process.cwd(), "public", "StoreFiles");
const jsonFilePath = path.join(uploadDir, "Documents.json");

if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const readJsonFile = () => {
  try {
    if (fs.existsSync(jsonFilePath)) {
      const data = fs.readFileSync(jsonFilePath, "utf8");
      return JSON.parse(data);
    }
  } catch (error) {
    console.error("Error reading JSON file:", error);
  }
  return [];
};

export async function GET() {
  try {
    if (!fs.existsSync(uploadDir)) {
      return NextResponse.json({ files: [] }, { status: 200 });
    }

    const files = fs.readdirSync(uploadDir);
    return NextResponse.json({ files }, { status: 200 });
  } catch (error) {
    console.error("Error retrieving files:", error);
    return NextResponse.json(
      { error: "Failed to retrieve files" },
      { status: 500 }
    );
  }
}


const writeJsonFile = (data: any) => {
  try {
    fs.writeFileSync(jsonFilePath, JSON.stringify(data, null, 2), "utf8");
  } catch (error) {
    console.error("Error writing JSON file:", error);
  }
};

export async function POST(request: Request) {
  try {
    const formData = await request.formData();

    const files = formData.getAll("file") as File[];
    const clientName = formData.get("clientName") as string;
    const userName = formData.get("userName") as string;
    const processType = formData.get("processType") as string;
    const description = formData.get("description") as string;

    if (
      !files.length ||
      !clientName ||
      !userName ||
      !processType ||
      !description
    ) {
      return NextResponse.json(
        {
          error:
            "At least one file, client name, user name, process type, and description are required",
        },
        { status: 400 }
      );
    }

    let filesData = readJsonFile();

    const maxId = filesData.reduce(
      (max: number, file: any) => Math.max(max, file.id || 0),
      0
    );

    let uploadedFiles: any[] = [];

    for (const file of files) {
      const filePath = path.join(uploadDir, file.name);
      const fileBuffer = await file.arrayBuffer();
      fs.writeFileSync(filePath, Buffer.from(fileBuffer));

      const newId = maxId + 1 + uploadedFiles.length;
      const fileNameWithoutExtension = file.name.replace(/\.[^/.]+$/, "");
      const fileExtension = file.name.split(".").pop()?.toUpperCase() || "UNKNOWN";
      const fileType = fileExtension === "DOCX" ? "DOCX" : fileExtension;
      const newFileEntry = {
        id: newId,
        fileName: fileNameWithoutExtension,
        userName,
        fileType,
        processType:"Invoice",
        clientName,
        description,
        created_at: new Date().toISOString(),
      };

      uploadedFiles.push(newFileEntry);
    }

    filesData = [...filesData, ...uploadedFiles];
    writeJsonFile(filesData);

    return NextResponse.json({
      message: "Files uploaded successfully",
      uploadedFiles,
      filesData,
    });
  } catch (error) {
    console.error("Error uploading files:", error);
    return NextResponse.json(
      { error: "Failed to upload files" },
      { status: 500 }
    );
  }
}