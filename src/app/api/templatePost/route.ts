import fs from "fs";
import path from "path";
import { NextResponse } from "next/server";

const filePath = path.join(
  process.cwd(),
  "public",
  "StoreFiles",
  "Template.json"
);

const readJsonFile = () => {
  try {
    if (fs.existsSync(filePath)) {
      return JSON.parse(fs.readFileSync(filePath, "utf-8"));
    }
  } catch (error) {
    console.error("Error reading JSON file:", error);
  }
  return [];
};

export async function GET(req: Request) {
  try{
    const jsonData = readJsonFile();
    return NextResponse.json(jsonData)
  }catch (error) {
    console.error("Error fetching data:", error);
    return NextResponse.json(
      { message: "Failed to fetch data." },
      { status: 500 }
    );
  }
}

const writeJsonFile = (data: any) => {
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), "utf-8");
  } catch (error) {
    console.error("Error writing JSON file:", error);
  }
};

export async function POST(req: Request) {
    try {
      const body = await req.json();
  
      if (!body.sections || !Array.isArray(body.sections)) {
        return NextResponse.json(
          { error: "Invalid input data" },
          { status: 400 }
        );
      }
  
      const jsonData = readJsonFile();
  

      const maxId =
        jsonData.length > 0
          ? Math.max(...jsonData.map((entry: any) => entry.id || 0))
          : 0;
      const newId = maxId + 1;
  
      const newEntry: any = {
        id: newId,
        templateName: body.templateName || "Unnamed Template",
        userName: body.userName || "XXXXX",
        created_at: new Date().toISOString().split("T")[0], 
        data: {
       
        },
      };
  
      body.sections.forEach((section: any) => {
        const sectionName = section.sectionName;
        const contains = section.contains?.trim();
        const matchValue = section.match_value;
        const extractFields = section.extract_fields
          .map((field: any) => field.value)
          .filter((value: any) => value !== null && value !== "");
  
        const sectionData: any = {
          type: "section",
          extract_fields: extractFields,
        };
  
        if (matchValue && matchValue.trim() !== "") {
          sectionData.match_value = matchValue;
        }
  
        if (contains && contains !== "") {
          sectionData.contains = [contains];
        }
  
        newEntry.data[sectionName] = sectionData;
      });
  
      jsonData.push(newEntry);
      writeJsonFile(jsonData);
  
      return NextResponse.json({ message: "Data saved successfully!" });
    } catch (error) {
      console.error("Error saving data:", error);
      return NextResponse.json(
        { message: "Failed to save data." },
        { status: 500 }
      );
    }
  }

  