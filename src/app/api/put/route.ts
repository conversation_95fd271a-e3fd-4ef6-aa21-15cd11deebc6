import { NextResponse } from "next/server";
import fs from "fs";
import path from "path";

const uploadDir = path.join(process.cwd(), "public", "StoreFiles");
const jsonFilePath = path.join(uploadDir, "Documents.json");

if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const readJsonFile = () => {
  try {
    if (fs.existsSync(jsonFilePath)) {
      const data = fs.readFileSync(jsonFilePath, "utf8");
      return JSON.parse(data);
    }
  } catch (error) {
    console.error("Error reading JSON file:", error);
  }
  return [];
};

const writeJsonFile = (data: any) => {
  try {
    fs.writeFileSync(jsonFilePath, JSON.stringify(data, null, 2), "utf8");
  } catch (error) {
    console.error("Error writing JSON file:", error);
  }
};

export async function PUT(request: Request) {
  try {
    const formData = await request.formData();

    const id = formData.get("id") as string;
    const files = formData.getAll("file") as File[];
    const clientName = formData.get("clientName") as string;
    const userName = formData.get("userName") as string;
    const processType = formData.get("processType") as string;
    const description = formData.get("description") as string;

    if (!id || !clientName || !userName || !processType || !description) {
      return NextResponse.json(
        {
          error:
            "ID, client name, user name, process type, and description are required",
        },
        { status: 400 }
      );
    }

    let filesData = readJsonFile();

    const fileIndex = filesData.findIndex(
      (file: any) => file.id === parseInt(id, 10)
    );

    if (fileIndex === -1) {
      return NextResponse.json({ error: "File not found" }, { status: 404 });
    }

    const updatedFile = {
      ...filesData[fileIndex],
      clientName,
      userName,
      processType: "Invoice",
      description,
    };

    if (files.length > 0) {
      for (const file of files) {
        const filePath = path.join(uploadDir, file.name);
        const fileBuffer = await file.arrayBuffer();
        fs.writeFileSync(filePath, Buffer.from(fileBuffer));

        const fileNameWithoutExtension = file.name.replace(/\.[^/.]+$/, "");

        const fileExtension =
          file.name.split(".").pop()?.toUpperCase() || "UNKNOWN";
        const fileType = fileExtension === "DOCX" ? "DOCX" : fileExtension;

        updatedFile.fileName = fileNameWithoutExtension;
        updatedFile.fileType = fileType;
      }
    }

    filesData[fileIndex] = updatedFile;

    writeJsonFile(filesData);

    return NextResponse.json({
      message: "File updated successfully",
      updatedFile,
    });
  } catch (error) {
    console.error("Error updating file:", error);
    return NextResponse.json(
      { error: "Failed to update file" },
      { status: 500 }
    );
  }
}
