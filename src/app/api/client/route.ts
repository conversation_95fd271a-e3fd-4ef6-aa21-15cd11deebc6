import { NextResponse } from "next/server";
import { promises as fs } from "fs";
import path from "path";

const filePath = path.join(process.cwd(), "src/app/db/Client.json");

export async function POST(req: Request) {
    try {
        const clientData = await req.json();
        let clients: any[] = [];

        try {
            const fileData = await fs.readFile(filePath, "utf-8");
            clients = JSON.parse(fileData || "[]");
        } catch {
            await fs.mkdir(path.dirname(filePath), { recursive: true });
            await fs.writeFile(filePath, "[]", "utf-8");
        }

        const newClient = { id: clients.length + 1, ...clientData };
        clients.push(newClient);

        await fs.writeFile(filePath, JSON.stringify(clients, null, 2), "utf-8");

        return NextResponse.json(newClient, { status: 201 });
    } catch (error) {
        console.error("Error adding client:", error);
        return NextResponse.json({ message: "Error adding client" }, { status: 500 });
    }
}
