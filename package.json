{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "set PORT=4000 && next start", "lint": "next lint"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^4.1.3", "@iconify/react": "^5.2.0", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.4.6", "@mui/material-nextjs": "^6.4.3", "@mui/x-data-grid": "^7.27.2", "@mui/x-date-pickers": "^7.27.1", "@tanstack/react-query": "^5.66.11", "bcryptjs": "^3.0.2", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "next": "15.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "swiper": "^11.2.4", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.0", "tailwindcss": "^4", "typescript": "^5"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}