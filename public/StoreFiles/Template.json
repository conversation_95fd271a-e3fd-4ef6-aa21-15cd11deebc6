[{"id": 1, "templateName": "template 11", "userName": "data userName", "created_at": "2025-03-14", "config": {"Transferor1": {"type": "section", "extract_fields": ["Name1", "Address1"], "contains": ["Individual1"]}, "Transferee1": {"type": "section", "extract_fields": ["Name11", "Address1"], "match_value": "HOMES VICTORIA1", "contains": ["Organisation1"]}, "Details of Transaction1": {"type": "section", "extract_fields": ["Total Sale Price11", "Deposit1", "Date of Contract1", "Date of possession/transfer1"]}, "Certification1": {"type": "section", "extract_fields": ["Certifier Name1", "Date1"]}}}, {"id": 2, "templateName": "sa", "userName": "data userName", "created_at": "2025-04-09", "data": {"as": {"type": "section", "extract_fields": ["as", "sa"], "match_value": "as", "contains": ["sa"]}}}, {"id": 3, "templateName": "document_type", "userName": "data userName", "created_at": "2025-04-09", "data": {"part1_transferor": {"type": "section", "extract_fields": ["surname", "given_names", "email", "future_correspondence_address", "representative_phone"]}, "part2_transferee": {"type": "section", "extract_fields": ["title", "company_or_trust", "state", "principal_residence_address"]}}}, {"id": 4, "templateName": "document_type", "userName": "data userName", "created_at": "2025-04-09", "data": {"part1_transferor": {"type": "section", "extract_fields": ["surname", "given_names", "email", "future_correspondence_address", "representative_phone"]}}}, {"id": 5, "templateName": "document_type", "userName": "data userName", "created_at": "2025-04-09", "data": {"part1_transferor": {"type": "section", "extract_fields": ["surname", "given_names", "email", "future_correspondence_address", "representative_phone"]}}}]